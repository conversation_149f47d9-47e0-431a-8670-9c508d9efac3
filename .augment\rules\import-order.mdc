# Python Import Order Rules for 智慧树查课项目

## 导入顺序规范

根据 PEP 8 标准和项目特点，Python 文件的导入应按以下顺序组织：

### 1. 标准库导入 (Standard Library Imports)
按字母顺序排列的 Python 标准库模块：

```python
import base64
import json
import math
import random
import re
import string
import time
import traceback
```

### 2. 第三方库导入 (Third-party Library Imports)
按字母顺序排列的第三方库：

```python
import cv2
import ddddocr
import execjs
import numpy as np
import requests
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from flask import Flask, request
from loguru import logger
from requests.adapters import HTTPAdapter
```

### 3. 本地应用导入 (Local Application Imports)
按模块层次和字母顺序排列：

#### 3.1 工具模块 (Utility Modules)
```python
from AES_PARAMS.AES_MAIN import *
```

#### 3.2 核心业务模块 (Core Business Modules)
```python
from MainSession.Session import StartSession
from UserLogin.Login import Status_Login
from UserLogin.phone import start_phone
from UserLogin.school import start_school
from UserList.UserKclist import StatusKclist
```

#### 3.3 验证码模块 (Captcha Modules)
```python
from YiDun import path as file_path
from YiDun.captcha import yidun
from YiDun.cptcha_dian import yidun_dian
from YiDun.gap import get_gap
from YiDun.打码 import dama
```

## 导入格式规范

### 分组分隔
- 每个导入分组之间使用一个空行分隔
- 同一分组内的导入不使用空行分隔

### 导入语句格式
- 优先使用 `import module` 形式
- 当需要导入特定函数/类时使用 `from module import item`
- 避免使用 `from module import *`，除非是专门的工具模块
- 长导入语句可以使用括号进行多行分割

### 别名使用
- 常用的长模块名可以使用标准别名：
  ```python
  import numpy as np
  from YiDun import path as file_path
  ```

## 项目特定规则

### 1. 加密模块导入
```python
from AES_PARAMS.AES_MAIN import AES_CBC_encrypt, AES_CBC_decrypt, encrypt_login
```

### 2. 会话管理导入
```python
from MainSession.Session import StartSession
```

### 3. 登录模块导入
```python
from UserLogin.Login import Status_Login
from UserLogin.phone import start_phone
from UserLogin.school import start_school
```

### 4. 验证码模块导入
```python
from YiDun.captcha import yidun
from YiDun.cptcha_dian import yidun_dian
```

## 示例：完整的导入顺序

```python
# 标准库导入
import json
import re
import time

# 第三方库导入
import requests
from flask import Flask, request
from loguru import logger

# 本地应用导入
from AES_PARAMS.AES_MAIN import AES_CBC_encrypt, AES_CBC_decrypt
from MainSession.Session import StartSession
from UserLogin.Login import Status_Login
from UserList.UserKclist import StatusKclist
from YiDun.captcha import yidun
```

## 注意事项

1. **避免循环导入**: 确保模块间的依赖关系是单向的
2. **延迟导入**: 对于可选依赖，可以在函数内部进行导入
3. **导入检查**: 使用 `isort` 工具自动格式化导入顺序
4. **文档更新**: 当添加新的依赖时，及时更新此规则文档
