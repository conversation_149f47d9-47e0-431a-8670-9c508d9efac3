import json
import re
import math
import random
import time
import traceback
from requests.adapters import HTTPAdapter
import execjs
import requests
import numpy as np
from YiDun import path as file_path
from YiDun.打码 import *
from loguru import logger

def proxies_ip():
    api = f"http://**********:5656/get_ip"
    r = requests.get(api).json()
    host = r['ip']
    IP = {"http": host, "https": host}
    return IP
def create_session_with_retry(retries=10, backoff_factor=0.1, status_forcelist=(500, 502, 504), session=None):
    """
    创建一个带有重连策略的Session对象，并禁用连接持久性。

    参数：
    - retries: 最大重试次数
    - backoff_factor: 重试之间的时间间隔因子，用于指数退避算法，控制重试时等待时间的增加速率
    - status_forcelist: 遇到哪些状态码时应该进行重试
    - session: 可选，如果提供了一个已存在的Session对象，则将重试策略应用于该对象，否则创建一个新的Session对象
    """
    if session is None:
        session = requests.Session()

    session.keep_alive = False  # 禁用连接持久性

    retry_strategy = requests.packages.urllib3.util.retry.Retry(
        total=retries,
        backoff_factor=backoff_factor,
        status_forcelist=status_forcelist
    )
    adapter = requests.adapters.HTTPAdapter(max_retries=retry_strategy)
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    return session

def get_secure_captcha(validate, fp, zoneId):
    """
    获取加密后的验证码
    :param validate: 验证成功返回的 validate
    :param fp: fingerprint
    :param zoneId: 一般是CN31
    :return: 加密后的验证码
    """
    ctx = get_ctx(file_path.CAPTCHA_SC_JS_PATH.replace("\API_ChaKe", ""))
    # ctx = get_ctx("js/secureCaptcha.js")
    return ctx.call('getSecureCaptcha', validate, fp, zoneId)

def get_ctx(path):
    """
    获取js对象
    :param path: js文件路径
    :return:
    """
    # with open(path, 'r', encoding='utf-8') as f:
    #     content = f.read()
    content =open(path, 'r', encoding='utf-8').read()
    ctx = execjs.compile(content)
    return ctx


def get_fp_callback():
    """
    获取fp、callback参数
    :return: fp, callback
    """
    ctx = get_ctx(file_path.CAPTCHA_FP_JS_PATH.replace("\API_ChaKe", ""))
    fp = ctx.call('get_fp')
    callback = ctx.call('get_callback')
    return fp, callback

def get_check_data(token, x, y):
    ctx = get_ctx(file_path.CAPTCHA_SC_JS_PATH.replace("\API_ChaKe", ""))
    return ctx.call('get_data', token, x, y)


class crypto_params:
    def __init__(self):
        self.ctx = get_ctx(file_path.CAPTCHA_CB_JS_PATH.replace("\API_ChaKe", ""))

    def get_cb(self):
        return self.ctx.call('cb')

    def get_data(self, token, trace, left):
        return self.ctx.call('get_data', token, trace, left)


class yidun_dian:
    def __init__(self, captcha_id='', captcha_data=None, actoken=False):
        """
        获取网易易盾的validate
        :param captcha_id: 网易易盾的CAPTCHA_ID
        :param captcha_data: 验证码参数设置
        :param actoken: 是否启用actoken
        """
        self.captcha_id = captcha_id
        self.captcha_data = {
            'v': 'e2891084',
            'version': '2.24.0',
            'type': '2',
        }
        self.crypto_param = crypto_params()
        self.token = None
        self.result = None
        self.secure_captcha = None
        self.fp = ""
        self.session = requests.session()#create_session_with_retry(retries=10, status_forcelist=(500, 502, 504, 443, 400))
        self.ip = {}
    def get_validate(self, actoken=None):
        
        self.headers = {
            'Accept': '*/*',
            'Host': 'c.dun.163yun.com',
            'Referer': '',
            'Pragma': 'no-cache',
            'Proxy-Connection': 'keep-alive',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36'
        }
        self.fp, callback = get_fp_callback()
        data = {
            "id": self.captcha_id,
            "fp": self.fp,
            "https": "true",
            "type": self.captcha_data.get('type', '2'),
            "version": self.captcha_data.get('version', '2.21.5'),
            "dpr": "1",
            "dev": "1",
            "cb": self.crypto_param.get_cb(),
            "ipv6": "false",
            "runEnv": "10",
            "group": "",
            "scene": "",
            "width": "320",
            "token": "",
            "referer": "",
            "callback": callback
        }
        try:
            r = self.session.get('https://c.dun.163.com/api/v3/get', params=data,proxies=self.ip)
        except:
            self.ip = proxies_ip()
            return self.get_validate()
        if r.status_code != 200:
            raise Exception("请求get接口错误！请检查captcha_id是否正确或网络是否可用。")
        data_ = json.loads(re.findall('.*?\((.*?)\);', r.text)[0])
        self.token = data_.get('data', {}).get('token', None)
        front = data_["data"]["front"]
        logger.error(f"front:{front}")
        img = requests.get(data_["data"]["bg"][0]).content
        # open('cptcha.jpg','wb').write(img)
        self.cptcha_post(img,front)
    def cptcha_post(self,img,front):
        data = dama(img, front)
        # data = YdmVerify().click_verify(img,extra=front)
        try:
            if data['code'] == 1:
                if len(data['data'].split(',')) == 2:
                    self.x = f"{float(float(data['data'].split(',')[0]) / 320):.2f}"
                    self.y = f"{float(float(data['data'].split(',')[1]) / 160):.2f}"
                    logger.success(f"点选风控验证 X轴：{self.x}，y轴：{self.y}")
                    data = get_check_data(self.token, self.x, self.y)
                    params = {
                        "referer": "",
                        "zoneId": "CN31",
                        "id": self.captcha_id,
                        "token": self.token,
                        "acToken": "undefined",
                        "data": data,
                        "width": "320",
                        "type": self.captcha_data.get('type', '2'),
                        "version": self.captcha_data.get('version', '2.21.5'),
                        "cb": self.crypto_param.get_cb(),
                        "extraData": "",
                        "bf": "0",
                        "runEnv": "10",
                        "sdkVersion": "undefined",
                        "callback": "__JSONP_48mk47t_1"
                    }
                    r = self.session.get('https://c.dun.163.com/api/v3/check', params=params,proxies=self.ip)
                    data = r.text[18:-2]
                    self.result = json.loads(data).get("data", {})
                    if self.result.get('result'):
                        self.secure_captcha = get_secure_captcha(
                            self.result.get('validate'),
                            self.fp,
                            self.result.get('zoneId')
                        )

                else:
                    self.ip = proxies_ip()
                    logger.error(f"点选验证码识别失败 正在重新获取")
                    return self.get_validate()
            else:
                self.ip = proxies_ip()
                logger.error(f"点选验证码识别失败 正在重新获取")
                return self.get_validate()
        except:
            self.ip = proxies_ip()
            logger.error(f"点选验证码识别失败 正在重新获取")
            return self.get_validate()
