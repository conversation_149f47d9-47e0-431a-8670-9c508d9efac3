from flask import Flask,request
import requests
from loguru import logger
from MainSession.Session import StartSession
from UserLogin.Login import Status_Login
from UserList.UserKclist import StatusKclist


app = Flask(__name__)
@app.route('/zhs_xfk', methods=['GET', 'POST'])
def zhs_xfk():
    school = request.args.get('school')
    try:school = school.encode('latin-1').decode('utf-8')
    except:school = school
    username = request.args.get('user')
    password = request.args.get('pass')
    session = requests.session()
    session = StartSession(session)
    login = Status_Login(session, school, username, password)
    UserList = []
    status, msg = login.login_main(UserList)
    if status:  # 登陆成功
        logger.success(msg.split(",")[0])
        usernaem = msg.split(",")[1]
        kclist = StatusKclist(session)
        list = kclist.app_get_kclist(UserList[0]['uuid'])
        kclist = kc_list_pipei(list)
        return {"code":1,"msg":"查询成功","userName":usernaem,"data":kclist}
    else:
        logger.error(msg)
        return {"code":-2,"msg":msg}


@app.route('/zhs_fzk', methods=['GET', 'POST'])
def zhs_fzk():
    school = request.args.get('school')
    try:school = school.encode('latin-1').decode('utf-8')
    except:school = school
    username = request.args.get('user')
    password = request.args.get('pass')
    session = requests.session()
    session = StartSession(session)
    login = Status_Login(session, school, username, password)
    UserList = []
    status, msg = login.login_main(UserList)
    if status:  # 登陆成功
        logger.success(msg.split(",")[0])
        usernaem = msg.split(",")[1]
        kclist = StatusKclist(session)
        list = kclist.get_app_kclist_fanzhuan(UserList[0]['uuid'])
        kclist = kc_list_canzhuan_pipei(list)
        return {"code":1,"msg":"查询成功","UserName":usernaem,"data":kclist}
    else:
        logger.error(msg)
        return {"code":-2,"msg":msg}
def kc_list_pipei(list_main):
    list = []
    for i in list_main:
        list.append({"name":i['courseName'],"img":i['img'],"id":i['courseId']})
    return list

def kc_list_canzhuan_pipei(list_main):
    list = []
    for i in list_main:
        list.append({"name":i['courseName'],"img":i['img'],"id":i['courseId']})
    return list

if __name__ == '__main__':app.run(host='0.0.0.0', port=7768, debug=True)