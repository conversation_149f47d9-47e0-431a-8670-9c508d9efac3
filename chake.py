import time

from flask import Flask, request, jsonify
import requests
from loguru import logger

from MainSession.Session import StartSession
from UserLogin.Login import Status_Login
from UserList.UserKclist import StatusKclist


app = Flask(__name__)
@app.route('/zhs_xfk', methods=['GET', 'POST'])
def zhs_xfk():
    school = request.args.get('school')
    try:school = school.encode('latin-1').decode('utf-8')
    except:school = school
    username = request.args.get('user')
    password = request.args.get('pass')
    session = requests.session()
    session = StartSession(session)
    login = Status_Login(session, school, username, password)
    UserList = []
    status, msg = login.login_main(UserList)
    if status:  # 登陆成功
        logger.success(msg.split(",")[0])
        usernaem = msg.split(",")[1]
        kclist = StatusKclist(session)
        list = kclist.app_get_kclist(UserList[0]['uuid'])
        kclist = kc_list_pipei(list)
        return {"code":1,"msg":"查询成功","userName":usernaem,"data":kclist}
    else:
        logger.error(msg)
        return {"code":-2,"msg":msg}


@app.route('/zhs_fzk', methods=['GET', 'POST'])
def zhs_fzk():
    school = request.args.get('school')
    try:school = school.encode('latin-1').decode('utf-8')
    except:school = school
    username = request.args.get('user')
    password = request.args.get('pass')
    session = requests.session()
    session = StartSession(session)
    login = Status_Login(session, school, username, password)
    UserList = []
    status, msg = login.login_main(UserList)
    if status:  # 登陆成功
        logger.success(msg.split(",")[0])
        usernaem = msg.split(",")[1]
        kclist = StatusKclist(session)
        list = kclist.get_app_kclist_fanzhuan(UserList[0]['uuid'])
        kclist = kc_list_canzhuan_pipei(list)
        return {"code":1,"msg":"查询成功","UserName":usernaem,"data":kclist}
    else:
        logger.error(msg)
        return {"code":-2,"msg":msg}
def kc_list_pipei(list_main):
    list = []
    for i in list_main:
        list.append({"name":i['courseName'],"img":i['img'],"id":i['courseId']})
    return list

def kc_list_canzhuan_pipei(list_main):
    list = []
    for i in list_main:
        list.append({"name":i['courseName'],"img":i['img'],"id":i['courseId']})
    return list


def validate_request_params(school, username, password):
    """验证请求参数"""
    errors = []

    if not school or school.strip() == "":
        errors.append("学校名称不能为空")

    if not username or username.strip() == "":
        errors.append("用户名不能为空")

    if not password or password.strip() == "":
        errors.append("密码不能为空")

    if len(password) < 6:
        errors.append("密码长度不能少于6位")

    return errors


def create_api_response(success=True, data=None, message="", code=1, username=""):
    """创建统一的API响应格式"""
    response = {
        "code": code if success else -2,
        "msg": message,
        "data": data if data is not None else []
    }

    if username:
        response["userName"] = username

    return jsonify(response)


@app.route('/api/v1/courses', methods=['POST'])
def get_courses():
    """
    统一的查课接口

    支持获取学分课和翻转课程列表

    请求参数:
    - school: 学校名称
    - username: 用户名（手机号或学号）
    - password: 密码
    - course_type: 课程类型 (xfk=学分课, fzk=翻转课, all=全部)

    返回格式:
    {
        "code": 1,
        "msg": "查询成功",
        "userName": "用户姓名",
        "data": [
            {
                "name": "课程名称",
                "img": "课程图片",
                "id": "课程ID",
                "type": "课程类型",
                "progress": "学习进度"
            }
        ]
    }
    """
    try:
        # 获取请求参数
        if request.is_json:
            data = request.get_json()
            school = data.get('school', '')
            username = data.get('username', '')
            password = data.get('password', '')
            course_type = data.get('course_type', 'all')
        else:
            school = request.form.get('school', '') or request.args.get('school', '')
            username = request.form.get('username', '') or request.args.get('username', '')
            password = request.form.get('password', '') or request.args.get('password', '')
            course_type = request.form.get('course_type', 'all') or request.args.get('course_type', 'all')

        # 处理中文编码
        try:
            school = school.encode('latin-1').decode('utf-8')
        except:
            pass

        # 参数验证
        validation_errors = validate_request_params(school, username, password)
        if validation_errors:
            return create_api_response(
                success=False,
                message="; ".join(validation_errors),
                code=-1
            )

        # 验证课程类型
        if course_type not in ['xfk', 'fzk', 'all']:
            return create_api_response(
                success=False,
                message="课程类型参数错误，支持: xfk(学分课), fzk(翻转课), all(全部)",
                code=-1
            )

        # 创建会话并登录
        session = requests.session()
        session = StartSession(session)
        login = Status_Login(session, school, username, password)
        UserList = []

        logger.info(f"用户 {username[:3]}*** 尝试登录学校: {school}")

        status, msg = login.login_main(UserList)

        if not status:
            logger.error(f"登录失败: {msg}")
            return create_api_response(
                success=False,
                message=msg,
                code=-2
            )

        # 登录成功，获取用户信息
        user_name = msg.split(",")[1] if "," in msg else "未知用户"
        user_uuid = UserList[0]['uuid']

        logger.success(f"用户 {user_name} 登录成功")

        # 获取课程列表
        kclist_service = StatusKclist(session)
        all_courses = []

        # 根据课程类型获取相应课程
        if course_type in ['xfk', 'all']:
            # 获取学分课
            xfk_list = kclist_service.app_get_kclist(user_uuid)
            for course in xfk_list:
                formatted_course = {
                    "name": course.get('courseName', ''),
                    "img": course.get('img', ''),
                    "id": course.get('courseId', ''),
                    "type": "学分课",
                    "recruitId": course.get('recruitId', ''),
                    "classId": course.get('classId', ''),
                    "schoolId": course.get('schoolid', ''),
                    "progress": course.get('kcprocess', 0)
                }
                all_courses.append(formatted_course)

        if course_type in ['fzk', 'all']:
            # 获取翻转课
            fzk_list = kclist_service.get_app_kclist_fanzhuan(user_uuid)
            for course in fzk_list:
                formatted_course = {
                    "name": course.get('courseName', ''),
                    "img": course.get('img', ''),
                    "id": course.get('courseId', ''),
                    "type": "翻转课",
                    "recruitId": course.get('recruitId', ''),
                    "classId": course.get('classId', '')
                }
                all_courses.append(formatted_course)

        logger.success(f"成功获取 {len(all_courses)} 门课程")

        return create_api_response(
            success=True,
            data=all_courses,
            message=f"查询成功，共找到 {len(all_courses)} 门课程",
            username=user_name
        )

    except Exception as e:
        logger.error(f"查课接口异常: {str(e)}")
        return create_api_response(
            success=False,
            message="系统内部错误，请稍后重试",
            code=-500
        )


@app.route('/api/v1/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        "status": "healthy",
        "service": "智慧树查课服务",
        "version": "1.0.0",
        "timestamp": int(time.time())
    })


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=7768, debug=True)