import base64

from Crypto.Cipher import AES
from Crypto.Util.Padding import pad,unpad
import json

def AES_CBC_encrypt(key, iv, text):
    if isinstance(text, dict):
        text = json.dumps(text, separators=(',', ':'))
    """AES的CBC模式加密字符串"""
    key = key.encode('utf-8')
    iv = iv.encode('utf-8')
    cipher = AES.new(key, AES.MODE_CBC, iv)
    padded_plaintext = pad(text.encode('utf-8'), AES.block_size)
    ciphertext = cipher.encrypt(padded_plaintext)
    encrypted_data = base64.b64encode(ciphertext).decode('utf-8')
    return encrypted_data

def AES_CBC_decrypt(key, iv, text):
    """AES的CBC模式解密字符串"""
    key = key.encode('utf-8')
    iv = iv.encode('utf-8')
    cipher = AES.new(key, AES.MODE_CBC, iv)
    ciphertext = base64.b64decode(text)
    padded_plaintext = cipher.decrypt(ciphertext)
    plaintext = unpad(padded_plaintext, AES.block_size).decode('utf-8')
    return plaintext

def encrypt_login(params, key_name):
    if isinstance(params, dict):
        params = json.dumps(params, separators=(',', ':'))
    return AES_CBC_encrypt(key=key_name, iv='563216db1e1744c7', text=params)