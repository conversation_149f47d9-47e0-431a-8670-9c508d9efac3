import json

from AES_PARAMS.AES_MAIN import *
import random
import string

def generate_random_imei():
    # IMEI号码通常由15位数字组成，但这里为了匹配示例中的格式，我们使用15位字符
    return ''.join(random.choices(string.ascii_letters + string.digits, k=15))


APP_LOGIN_API = f"https://appcomm-user-api.zhihuishu.com/app-commserv-user/gateway/f/newLogin/byStudentCodeV5"
APP_SCHOOL_CODE = f"https://appstudent-api.zhihuishu.com/appstudent/gateway/f/v1/appserver/studentRegister/findSchoolV2"


class start_school:
    def __init__(self,session,school,username,password):
        self.session = session
        self.school = school
        self.username = username
        self.password = password
    def school_id(self):
        jsonStr = "{\"page\":\"1\",\"schoolNameKey\":\"学校\",\"pageSize\":\"20000\"}".replace("学校",self.school)
        stcretstr = AES_CBC_encrypt(key="59d78f063c204a99",iv="563216db1e1744c7",text=f"{jsonStr}21515340800")
        params = {
            "jsonStr": jsonStr,
            "osVersion": "2",
            "secretStr": stcretstr,
            "timeNote": "1515340800"
        }
        r = self.session.post(APP_SCHOOL_CODE,params=params,headers={
            "User-Agent": "android.zhihuishu.coma_zd",
            "z-version": "a_226",
            "Content-Type": "application/x-www-form-urlencoded"
        }).json()
        if r['rt'] is None:return False,{"rt":{"status":-200}}
        else:return True,r["rt"][0]["schoolId"]
    def login_status(self,token,validate):
        sta,SchoolID = self.school_id()
        if sta:
            imei = generate_random_imei() # 08b2e2ce2824cc70
            data = {
                "deviceId": imei,
                "password": self.password,
                "appType": "ZD_A",
                "imei": imei,
                "token": token,
                "validate": validate,
                "version": "6",
                "zhsDeviceId": imei,
                "schoolId": str(SchoolID),
                "code": self.username
            }
            secretStr = encrypt_login(data,'59d78f063c204a99')
            params = {
                "osVersion": "2",
                "secretStr": secretStr
            }
            r = self.session.post(APP_LOGIN_API,data=params,headers={
                "User-Agent": "android.zhihuishu.coma_zd",
                "z-version": "a_226",
                "Content-Type": "application/x-www-form-urlencoded",
                "Host": "appcomm-user-api.zhihuishu.com"
                }).json()
            return r
        else:return SchoolID