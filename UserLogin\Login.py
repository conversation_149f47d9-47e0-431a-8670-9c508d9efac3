import json
import re
import time
from AES_PARAMS.AES_MAIN import *
import requests
from YiDun.打码 import dama
from UserLogin.phone import start_phone
from UserLogin.school import start_school
from YiDun.captcha import yidun
from YiDun.cptcha_dian import yidun_dian
from loguru import logger
class Status_Login:
    def __init__(self, session,school,username, password):
        self.session = session
        self.school = school
        self.username = username
        self.password = password

    def get_cptcha(self):
        yidun_call = yidun(captcha_id='75f9f716460a422f89a628f50fd8cc2b')
        yidun_call.get_validate()
        img_data = yidun_call.img_data
        self.token = img_data['token']
        self.validate = img_data['validate']

    def get_cptcha_dian(self,captcha_id='4da3050565514a35a50541b0e1f54538'):
        self.SI = yidun_dian(captcha_id=captcha_id)
        self.SI.captcha_data['type'] = '11'
        self.SI.captcha_data['v'] = 'af2952a4'
        self.position = {'x': 0.00, 'y': 0.00}
        self.captcha_data = None
        while True:
            self.SI.get_validate()
            if self.SI.secure_captcha is None:
                continue
            break
    def login_dian(self,uid):
        api = f"https://appcomm-user-api.zhihuishu.com/app-commserv-user/gateway/f/newLogin/codeValidateV1"
        params = {
            "code": self.SI.secure_captcha,
            "nt": int(time.time()*1000),
            "phone": self.username,
            "uid": uid
        }
        r = self.session.post(api, params=params).json()
        self.studentInfo(r)
    def studentInfo(self,r):
        key = '59d78f063c204a99'
        iv = '563216db1e1744c7'
        try:user_inof_data = r["rt"]["userInfo"]
        except:user_inof_data = r["rt"]['userAndAuthInfo']["userInfo"]
        user_inof = AES_CBC_decrypt(key, iv, user_inof_data)
        try:school_inof_data = r["rt"]["authInfo"]
        except:school_inof_data = r["rt"]['userAndAuthInfo']["authInfo"]
        school_inof = AES_CBC_decrypt(key, iv, school_inof_data)
        tokn_inof = AES_CBC_decrypt(key, iv, r["rt"]["jinfo"])
        self.userId = json.loads(user_inof)['userId']
        self.userUUID = json.loads(user_inof)['uuid']
        self.UsernNme = json.loads(user_inof)['realName']
        self.SchoolId = json.loads(school_inof)['id']
        self.SchoolName = json.loads(school_inof)['name']
        self.cookie_token = json.loads(tokn_inof)['jwt']
        self.session.headers_update({"Authorization":self.cookie_token})
        logger.success(f"学生姓名:{self.UsernNme} 学校:{self.SchoolName} 学校ID：{self.SchoolId}")
        self.UserList.append({"uuid": self.userUUID,"userid":self.userId,"schoolId":self.SchoolId})
    def login_main(self,UserList):
        self.UserList = UserList
        self.get_cptcha()
        pattern = r'^1[3456789]\d{9}$'
        # 使用 re.match() 函数进行匹配
        if re.match(pattern, self.username):
            status_data = start_phone(self.session,self.username,self.password).login_status(self.token,self.validate)
        else:
            status_data = start_school(self.session,self.school,self.username,self.password).login_status(self.token,self.validate)
        if status_data["rt"]["status"] == -9:  # 异地登陆
            logger.error("正在准备登陆风控打码")
            userUUID = status_data['rt']['userUUID']
            self.username = status_data['rt']['phone']
            self.get_cptcha_dian(captcha_id='4da3050565514a35a50541b0e1f54538')
            self.login_dian(userUUID)
            return True,f'登陆成功,{self.UsernNme}'
        elif status_data['rt']['status'] == 1: #   登陆成功'
            self.studentInfo(status_data)
            return True, f'登陆成功,{self.UsernNme}'
        elif status_data['rt']['status'] == -2: #   密码错误
            return False,'账号密码错误'
        elif status_data['rt']['status'] == -200: #   自定义返回值（学校错误）
            return False,'学校信息错误'
        else:
            print(status_data)
            return False,status_data['rt']['status']
