# 智慧树查课API接口

## 🚀 快速开始

### 启动服务
```bash
python chake.py
```
服务将在 `http://localhost:7768` 启动

### 快速测试
使用下面的 cURL 命令进行快速测试（请替换为真实的学校和账号信息）：
```bash
# 测试学分课程查询
curl "http://localhost:7768/zhs_xfk?school=北京大学&user=your_username&pass=your_password"

# 测试翻转课程查询
curl "http://localhost:7768/zhs_fzk?school=北京大学&user=your_username&pass=your_password"
```

## 📋 接口概览

| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 学分课程 | GET/POST | `/zhs_xfk` | 获取学分课程列表 |
| 翻转课程 | GET/POST | `/zhs_fzk` | 获取翻转课程列表 |

## 🎯 接口详情

### 1. 学分课程查询接口

**URL**: `GET/POST /zhs_xfk`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| school | string | 是 | 学校名称 |
| user | string | 是 | 用户名（支持手机号或学号） |
| pass | string | 是 | 密码 |

**响应格式**:

成功响应 (code: 1):
```json
{
    "code": 1,
    "msg": "查询成功",
    "userName": "用户真实姓名",
    "data": [
        {
            "name": "课程名称",
            "img": "课程图片URL",
            "id": "课程ID"
        }
    ]
}
```

失败响应 (code: -2):
```json
{
    "code": -2,
    "msg": "错误信息"
}
```

### 2. 翻转课程查询接口

**URL**: `GET/POST /zhs_fzk`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| school | string | 是 | 学校名称 |
| user | string | 是 | 用户名（支持手机号或学号） |
| pass | string | 是 | 密码 |

**响应格式**:

成功响应 (code: 1):
```json
{
    "code": 1,
    "msg": "查询成功",
    "UserName": "用户真实姓名",
    "data": [
        {
            "name": "课程名称",
            "img": "课程图片URL",
            "id": "课程ID"
        }
    ]
}
```

失败响应 (code: -2):
```json
{
    "code": -2,
    "msg": "错误信息"
}
```

## 🔧 cURL 使用示例

### 基础配置

设置基础变量（请替换为真实信息）：
```bash
# 服务器地址
BASE_URL="http://localhost:7768"

# 用户信息（请替换为真实信息）
SCHOOL="北京大学"
USER="your_username"  # 手机号或学号
PASS="your_password"
```

### 1. 学分课程查询

#### GET 请求方式:
```bash
curl -X GET "${BASE_URL}/zhs_xfk?school=${SCHOOL}&user=${USER}&pass=${PASS}"
```

#### POST 请求方式:
```bash
curl -X POST "${BASE_URL}/zhs_xfk?school=${SCHOOL}&user=${USER}&pass=${PASS}"
```

#### 完整示例:
```bash
curl -X GET \
  "http://localhost:7768/zhs_xfk?school=北京大学&user=13800138000&pass=password123" \
  -H "Accept: application/json" \
  -H "User-Agent: curl/7.68.0"
```

### 2. 翻转课程查询

#### GET 请求方式:
```bash
curl -X GET "${BASE_URL}/zhs_fzk?school=${SCHOOL}&user=${USER}&pass=${PASS}"
```

#### POST 请求方式:
```bash
curl -X POST "${BASE_URL}/zhs_fzk?school=${SCHOOL}&user=${USER}&pass=${PASS}"
```

#### 完整示例:
```bash
curl -X GET \
  "http://localhost:7768/zhs_fzk?school=清华大学&user=2021001001&pass=mypassword" \
  -H "Accept: application/json" \
  -H "User-Agent: curl/7.68.0"
```

### 3. 带超时设置的请求
```bash
curl -X GET \
  "${BASE_URL}/zhs_xfk?school=${SCHOOL}&user=${USER}&pass=${PASS}" \
  --connect-timeout 30 \
  --max-time 60 \
  -H "Accept: application/json"
```

### 4. 保存响应到文件
```bash
# 保存学分课程查询结果
curl -X GET \
  "${BASE_URL}/zhs_xfk?school=${SCHOOL}&user=${USER}&pass=${PASS}" \
  -o "xfk_courses.json"

# 保存翻转课程查询结果
curl -X GET \
  "${BASE_URL}/zhs_fzk?school=${SCHOOL}&user=${USER}&pass=${PASS}" \
  -o "fzk_courses.json"
```

### 5. 详细输出模式
```bash
curl -X GET \
  "${BASE_URL}/zhs_xfk?school=${SCHOOL}&user=${USER}&pass=${PASS}" \
  -v \
  -H "Accept: application/json"
```

## 📊 错误码说明

| 错误码 | 描述 | 常见原因 | 解决方案 |
|--------|------|----------|----------|
| 1 | 查询成功 | - | - |
| -2 | 查询失败 | 账号密码错误、学校信息错误、网络问题 | 检查学校、用户名、密码是否正确 |

## 🔍 参数说明

### 用户名格式
- **手机号**: 11位数字，如 `13800138000`
- **学号**: 非手机号格式，如 `2021001001`

系统会自动识别用户名类型并选择相应的登录方式：
- 手机号格式：使用手机号登录接口
- 非手机号格式：使用学号登录接口

### 学校名称
- 支持中文学校名称
- 系统会自动处理编码转换
- 建议使用完整的学校名称，如"北京大学"而不是"北大"

## 🛠️ 高级用法

### 1. 批量查询脚本
```bash
#!/bin/bash

# 用户信息数组（格式：学校,用户名,密码）
users=(
    "北京大学,13800138000,password1"
    "清华大学,2021001001,password2"
    "复旦大学,18912345678,password3"
)

# 遍历用户进行查询
for user_info in "${users[@]}"; do
    IFS=',' read -r school username password <<< "$user_info"

    echo "查询用户: $username@$school"

    # 查询学分课程
    curl -X GET "http://localhost:7768/zhs_xfk?school=$school&user=$username&pass=$password" \
      -o "xfk_${username}.json"

    # 查询翻转课程
    curl -X GET "http://localhost:7768/zhs_fzk?school=$school&user=$username&pass=$password" \
      -o "fzk_${username}.json"

    echo "结果已保存到 xfk_${username}.json 和 fzk_${username}.json"
    echo "---"
done
```

### 2. PowerShell 示例 (Windows)
```powershell
# PowerShell 脚本示例
$baseUrl = "http://localhost:7768"
$school = "北京大学"
$user = "13800138000"
$pass = "password123"

# 查询学分课程
$xfkUrl = "$baseUrl/zhs_xfk?school=$school&user=$user&pass=$pass"
$xfkResponse = Invoke-RestMethod -Uri $xfkUrl -Method Get

# 查询翻转课程
$fzkUrl = "$baseUrl/zhs_fzk?school=$school&user=$user&pass=$pass"
$fzkResponse = Invoke-RestMethod -Uri $fzkUrl -Method Get

# 输出结果
Write-Host "学分课程查询结果:"
$xfkResponse | ConvertTo-Json -Depth 10

Write-Host "翻转课程查询结果:"
$fzkResponse | ConvertTo-Json -Depth 10
```

### 3. Python requests 示例
```python
import requests
import json

# API配置
base_url = "http://localhost:7768"
params = {
    "school": "北京大学",
    "user": "13800138000",
    "pass": "password123"
}

try:
    # 查询学分课程
    xfk_response = requests.get(f"{base_url}/zhs_xfk", params=params, timeout=60)
    xfk_result = xfk_response.json()

    if xfk_result["code"] == 1:
        print(f"学分课程查询成功: {xfk_result['msg']}")
        print(f"用户: {xfk_result['userName']}")
        print(f"课程数量: {len(xfk_result['data'])}")

        for course in xfk_result["data"]:
            print(f"- {course['name']}")
    else:
        print(f"学分课程查询失败: {xfk_result['msg']}")

    # 查询翻转课程
    fzk_response = requests.get(f"{base_url}/zhs_fzk", params=params, timeout=60)
    fzk_result = fzk_response.json()

    if fzk_result["code"] == 1:
        print(f"翻转课程查询成功: {fzk_result['msg']}")
        print(f"用户: {fzk_result['UserName']}")
        print(f"课程数量: {len(fzk_result['data'])}")

        for course in fzk_result["data"]:
            print(f"- {course['name']}")
    else:
        print(f"翻转课程查询失败: {fzk_result['msg']}")

except requests.exceptions.RequestException as e:
    print(f"请求异常: {e}")
```

## ⚠️ 注意事项

### 1. 性能考虑
- 接口包含登录验证和验证码处理，响应时间通常为 10-30 秒
- 建议设置 60 秒超时时间
- 避免频繁调用，建议间隔至少 10 秒
- 系统内置重试机制，会自动处理网络异常

### 2. 安全建议
- 生产环境建议使用 HTTPS
- 不要在日志或脚本中明文记录密码
- 注意保护用户隐私信息
- 建议实现 API 访问频率限制

### 3. 编码处理
- 支持 UTF-8 编码的中文学校名称
- 系统会自动处理编码转换
- URL 参数中的中文会被自动编码

### 4. 验证码处理
- 系统集成了易盾验证码自动处理
- 支持滑动验证码和点选验证码
- 异地登录会自动触发风控验证
- 无需手动干预验证码过程

## 🚀 部署方式

### 开发环境
```bash
python chake.py
```
服务将在 `http://localhost:7768` 启动

### 生产环境 - Gunicorn
```bash
gunicorn -c gunicorn_conf.py chake:app
```

### 生产环境 - uWSGI
```bash
uwsgi --ini uwsgi.ini
```

## � 故障排除

### 常见问题

**Q: 接口响应时间很长？**
A: 正常现象。接口包含完整的登录验证过程，包括验证码处理，正常响应时间为10-30秒。

**Q: 返回"账号密码错误"？**
A: 请检查：
- 学校名称是否准确（建议使用完整名称）
- 用户名格式是否正确（手机号或学号）
- 密码是否正确
- 网络连接是否稳定

**Q: 中文学校名称出现乱码？**
A: 确保使用 UTF-8 编码，系统会自动处理编码转换。在 cURL 中可以直接使用中文。

**Q: 接口返回系统错误？**
A: 可能的原因：
- 智慧树服务器临时不可用
- 网络连接问题
- 验证码服务异常
建议稍后重试。

### 调试技巧

1. **使用详细输出模式**：
```bash
curl -v "http://localhost:7768/zhs_xfk?school=北京大学&user=test&pass=test"
```

2. **检查服务状态**：
```bash
# 检查服务是否启动
curl -I http://localhost:7768/zhs_xfk

# 查看服务日志
tail -f /path/to/logfile
```

3. **测试网络连接**：
```bash
# 测试基本连接
curl -I http://localhost:7768

# 测试带参数的请求
curl "http://localhost:7768/zhs_xfk?school=test&user=test&pass=test"
```

## 🤝 技术支持

如遇到问题，请按以下步骤排查：

1. **检查服务状态**：确认服务已正常启动在端口 7768
2. **验证参数**：确保学校名称、用户名、密码格式正确
3. **网络检查**：确认网络连接正常，可访问智慧树官网
4. **日志查看**：查看服务运行日志，了解具体错误信息
5. **重试机制**：如遇临时错误，可稍后重试

## 📚 相关文件

- `curl_examples.sh` - 详细的 cURL 示例集合
- `chake.py` - 主服务文件
- `gunicorn_conf.py` - Gunicorn 配置文件
- `uwsgi.ini` - uWSGI 配置文件
- `.augment/rules/` - 代码规范和架构指南

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和智慧树平台的使用条款。
