# 智慧树查课API接口

## 🚀 快速开始

### 启动服务
```bash
python chake.py
```
服务将在 `http://localhost:7768` 启动

### 快速测试
```bash
# Linux/Mac
chmod +x quick_test.sh
./quick_test.sh

# Windows
quick_test.bat
```

## 📋 接口概览

| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 统一查课 | POST | `/api/v1/courses` | 获取课程列表（推荐） |
| 健康检查 | GET | `/api/v1/health` | 服务状态检查 |
| 学分课 | GET/POST | `/zhs_xfk` | 兼容接口 |
| 翻转课 | GET/POST | `/zhs_fzk` | 兼容接口 |

## 🎯 主要接口详情

### 统一查课接口

**URL**: `POST /api/v1/courses`

**请求参数**:
```json
{
    "school": "学校名称",
    "username": "用户名（手机号或学号）",
    "password": "密码",
    "course_type": "课程类型（xfk/fzk/all）"
}
```

**响应格式**:
```json
{
    "code": 1,
    "msg": "查询成功，共找到 5 门课程",
    "userName": "张三",
    "data": [
        {
            "name": "课程名称",
            "img": "课程图片URL",
            "id": "课程ID",
            "type": "课程类型",
            "recruitId": "招募ID",
            "classId": "班级ID",
            "progress": 75
        }
    ]
}
```

## 🔧 cURL 使用示例

### 1. 基本查课请求
```bash
curl -X POST http://localhost:7768/api/v1/courses \
  -H "Content-Type: application/json" \
  -d '{
    "school": "北京大学",
    "username": "13800138000",
    "password": "password123",
    "course_type": "all"
  }'
```

### 2. 仅获取学分课
```bash
curl -X POST http://localhost:7768/api/v1/courses \
  -H "Content-Type: application/json" \
  -d '{
    "school": "清华大学",
    "username": "**********",
    "password": "mypassword",
    "course_type": "xfk"
  }'
```

### 3. 仅获取翻转课
```bash
curl -X POST http://localhost:7768/api/v1/courses \
  -H "Content-Type: application/json" \
  -d '{
    "school": "复旦大学",
    "username": "18912345678",
    "password": "123456789",
    "course_type": "fzk"
  }'
```

### 4. 表单格式请求
```bash
curl -X POST http://localhost:7768/api/v1/courses \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "school=北京大学&username=13800138000&password=password123&course_type=all"
```

### 5. 健康检查
```bash
curl -X GET http://localhost:7768/api/v1/health
```

## 📊 错误码说明

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 1 | 成功 | - |
| -1 | 参数错误 | 检查必填参数是否完整 |
| -2 | 登录失败 | 检查学校、用户名、密码是否正确 |
| -500 | 系统错误 | 稍后重试或联系管理员 |

## 🛠️ 开发工具

### Postman 集合
导入 `智慧树查课API.postman_collection.json` 到 Postman 中进行测试

### 测试脚本
- `quick_test.sh` - Linux/Mac 快速测试脚本
- `quick_test.bat` - Windows 快速测试脚本
- `curl_examples.sh` - 详细的 cURL 示例集合

## 🔍 参数说明

### course_type 参数
- `xfk`: 仅获取学分课
- `fzk`: 仅获取翻转课
- `all`: 获取全部课程（默认值）

### 用户名格式
- **手机号**: 11位数字，如 `13800138000`
- **学号**: 非手机号格式，如 `**********`

系统会自动识别用户名类型并选择相应的登录方式。

## ⚠️ 注意事项

### 1. 性能考虑
- 接口包含登录验证，响应时间通常为 5-15 秒
- 建议设置 30 秒超时时间
- 避免频繁调用，建议间隔至少 5 秒

### 2. 安全建议
- 生产环境使用 HTTPS
- 不要在日志中记录密码
- 考虑实现 API 访问频率限制

### 3. 编码处理
- 支持 UTF-8 编码的中文学校名称
- 系统自动处理编码转换

## 🚀 部署方式

### 开发环境
```bash
python chake.py
```

### 生产环境 - Gunicorn
```bash
gunicorn -c gunicorn_conf.py chake:app
```

### 生产环境 - uWSGI
```bash
uwsgi --ini uwsgi.ini
```

## 📝 更新日志

### v1.0.0 (2024-01-01)
- ✅ 新增统一查课接口 `/api/v1/courses`
- ✅ 新增健康检查接口 `/api/v1/health`
- ✅ 改进参数验证和错误处理
- ✅ 优化响应格式和日志记录
- ✅ 保持向后兼容性
- ✅ 提供完整的测试工具和文档

## 🤝 技术支持

如有问题，请检查：
1. 服务是否正常启动
2. 网络连接是否正常
3. 参数格式是否正确
4. 学校名称、用户名、密码是否准确

## 📚 相关文件

- `API_Documentation.md` - 详细API文档
- `curl_examples.sh` - cURL示例集合
- `quick_test.sh` / `quick_test.bat` - 快速测试脚本
- `智慧树查课API.postman_collection.json` - Postman测试集合
- `.augment/rules/` - 代码规范和架构指南
