([('chake.exe', 'E:\\智慧树爬题库\\智慧树查课\\build\\chake\\chake.exe', 'EXECUTABLE'),
  ('python37.dll', 'd:\\python\\python37.dll', 'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'd:\\python\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('ucrtbase.dll', 'D:\\jdk22\\bin\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('Crypto\\Cipher\\_ARC4.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_ARC4.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_RIPEMD160.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_RIPEMD160.pyd',
   'BINARY'),
  ('cv2\\opencv_videoio_ffmpeg490_64.dll',
   'd:\\python\\lib\\site-packages\\cv2\\opencv_videoio_ffmpeg490_64.dll',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD4.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_MD4.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_poly1305.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_poly1305.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_chacha20.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_chacha20.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_x25519.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\PublicKey\\_x25519.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2b.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_BLAKE2b.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2s.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_BLAKE2s.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cfb.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_cfb.pyd',
   'BINARY'),
  ('Crypto\\Util\\_cpuid_c.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Util\\_cpuid_c.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA1.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_SHA1.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD5.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_MD5.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_pkcs1_decode.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_pkcs1_decode.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD2.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_MD2.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ec_ws.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\PublicKey\\_ec_ws.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_Salsa20.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_Salsa20.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA512.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_SHA512.pyd',
   'BINARY'),
  ('Crypto\\Protocol\\_scrypt.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Protocol\\_scrypt.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_blowfish.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_blowfish.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_portable.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_ghash_portable.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA384.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_SHA384.pyd',
   'BINARY'),
  ('libopenblas.XWYDX2IKJW2NMTWSFYNGFUWKQU3LYTCZ.gfortran-win_amd64.dll',
   'd:\\python\\lib\\site-packages\\numpy\\.libs\\libopenblas.XWYDX2IKJW2NMTWSFYNGFUWKQU3LYTCZ.gfortran-win_amd64.dll',
   'BINARY'),
  ('cv2\\opencv_videoio_ffmpeg480_64.dll',
   'd:\\python\\lib\\site-packages\\cv2\\opencv_videoio_ffmpeg480_64.dll',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aesni.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_aesni.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ctr.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_ctr.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des3.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_des3.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aes.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_aes.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cast.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_cast.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ecb.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_ecb.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed448.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\PublicKey\\_ed448.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ocb.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_ocb.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ofb.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_ofb.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA256.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_SHA256.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cbc.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_cbc.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_des.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed25519.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\PublicKey\\_ed25519.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_clmul.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_ghash_clmul.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_keccak.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_keccak.pyd',
   'BINARY'),
  ('Crypto\\Math\\_modexp.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Math\\_modexp.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA224.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_SHA224.pyd',
   'BINARY'),
  ('Crypto\\Util\\_strxor.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Util\\_strxor.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_arc2.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_arc2.pyd',
   'BINARY'),
  ('select.pyd', 'd:\\python\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'd:\\python\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'd:\\python\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'd:\\python\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'd:\\python\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'd:\\python\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'd:\\python\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('pyexpat.pyd', 'd:\\python\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'd:\\python\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'd:\\python\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_decimal.pyd', 'd:\\python\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'd:\\python\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_cffi_backend.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\_cffi_backend.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'd:\\python\\lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\lapack_lite.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\linalg\\lapack_lite.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\random\\mtrand.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\random\\_sfc64.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\random\\_philox.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\random\\_pcg64.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\random\\_mt19937.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\random\\bit_generator.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\random\\_generator.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\random\\_common.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\charset_normalizer\\md.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('cv2\\cv2.pyd', 'd:\\python\\lib\\site-packages\\cv2\\cv2.pyd', 'EXTENSION'),
  ('PIL\\_webp.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\PIL\\_webp.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\PIL\\_imagingtk.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\PIL\\_imagingcms.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\PIL\\_imaging.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('onnxruntime\\capi\\onnxruntime_pybind11_state.pyd',
   'd:\\python\\lib\\site-packages\\onnxruntime\\capi\\onnxruntime_pybind11_state.pyd',
   'EXTENSION'),
  ('_overlapped.pyd', 'd:\\python\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'd:\\python\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('markupsafe\\_speedups.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\markupsafe\\_speedups.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('libcrypto-1_1.dll', 'd:\\python\\DLLs\\libcrypto-1_1.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libssl-1_1.dll', 'd:\\python\\DLLs\\libssl-1_1.dll', 'BINARY'),
  ('python3.dll', 'd:\\python\\python3.dll', 'BINARY'),
  ('MSVCP140.dll', 'C:\\WINDOWS\\system32\\MSVCP140.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'C:\\WINDOWS\\system32\\VCRUNTIME140_1.dll', 'BINARY'),
  ('AES_PARAMS\\AES_MAIN.py',
   'E:\\智慧树爬题库\\智慧树查课\\AES_PARAMS\\AES_MAIN.py',
   'DATA'),
  ('AES_PARAMS\\__pycache__\\AES_MAIN.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\AES_PARAMS\\__pycache__\\AES_MAIN.cpython-37.pyc',
   'DATA'),
  ('MainSession\\Session.py',
   'E:\\智慧树爬题库\\智慧树查课\\MainSession\\Session.py',
   'DATA'),
  ('MainSession\\__pycache__\\Session.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\MainSession\\__pycache__\\Session.cpython-37.pyc',
   'DATA'),
  ('UserList\\UserKclist.py',
   'E:\\智慧树爬题库\\智慧树查课\\UserList\\UserKclist.py',
   'DATA'),
  ('UserList\\__pycache__\\UserKclist.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\UserList\\__pycache__\\UserKclist.cpython-37.pyc',
   'DATA'),
  ('UserLogin\\Login.py', 'E:\\智慧树爬题库\\智慧树查课\\UserLogin\\Login.py', 'DATA'),
  ('UserLogin\\__pycache__\\Login.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\UserLogin\\__pycache__\\Login.cpython-37.pyc',
   'DATA'),
  ('UserLogin\\__pycache__\\phone.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\UserLogin\\__pycache__\\phone.cpython-37.pyc',
   'DATA'),
  ('UserLogin\\__pycache__\\school.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\UserLogin\\__pycache__\\school.cpython-37.pyc',
   'DATA'),
  ('UserLogin\\phone.py', 'E:\\智慧树爬题库\\智慧树查课\\UserLogin\\phone.py', 'DATA'),
  ('UserLogin\\school.py', 'E:\\智慧树爬题库\\智慧树查课\\UserLogin\\school.py', 'DATA'),
  ('UserLogin\\转换cookie.py',
   'E:\\智慧树爬题库\\智慧树查课\\UserLogin\\转换cookie.py',
   'DATA'),
  ('YiDun\\__init__.py', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\__init__.py', 'DATA'),
  ('YiDun\\__pycache__\\__init__.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\YiDun\\__pycache__\\__init__.cpython-37.pyc',
   'DATA'),
  ('YiDun\\__pycache__\\captcha.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\YiDun\\__pycache__\\captcha.cpython-37.pyc',
   'DATA'),
  ('YiDun\\__pycache__\\cptcha_dian.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\YiDun\\__pycache__\\cptcha_dian.cpython-37.pyc',
   'DATA'),
  ('YiDun\\__pycache__\\gap.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\YiDun\\__pycache__\\gap.cpython-37.pyc',
   'DATA'),
  ('YiDun\\__pycache__\\path.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\YiDun\\__pycache__\\path.cpython-37.pyc',
   'DATA'),
  ('YiDun\\__pycache__\\打码.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\YiDun\\__pycache__\\打码.cpython-37.pyc',
   'DATA'),
  ('YiDun\\captcha.py', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\captcha.py', 'DATA'),
  ('YiDun\\cptcha_dian.py', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\cptcha_dian.py', 'DATA'),
  ('YiDun\\gap.py', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\gap.py', 'DATA'),
  ('YiDun\\js\\actoken.js', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\js\\actoken.js', 'DATA'),
  ('YiDun\\js\\cb.js', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\js\\cb.js', 'DATA'),
  ('YiDun\\js\\fp.js', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\js\\fp.js', 'DATA'),
  ('YiDun\\js\\secureCaptcha.js',
   'E:\\智慧树爬题库\\智慧树查课\\YiDun\\js\\secureCaptcha.js',
   'DATA'),
  ('YiDun\\path.py', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\path.py', 'DATA'),
  ('YiDun\\打码.py', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\打码.py', 'DATA'),
  ('base_library.zip',
   'E:\\智慧树爬题库\\智慧树查课\\build\\chake\\base_library.zip',
   'DATA'),
  ('importlib_metadata-4.13.0.dist-info\\METADATA',
   'd:\\python\\lib\\site-packages\\importlib_metadata-4.13.0.dist-info\\METADATA',
   'DATA'),
  ('importlib_metadata-4.13.0.dist-info\\LICENSE',
   'd:\\python\\lib\\site-packages\\importlib_metadata-4.13.0.dist-info\\LICENSE',
   'DATA'),
  ('certifi\\cacert.pem',
   'd:\\python\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('importlib_metadata-4.13.0.dist-info\\top_level.txt',
   'd:\\python\\lib\\site-packages\\importlib_metadata-4.13.0.dist-info\\top_level.txt',
   'DATA'),
  ('cv2\\load_config_py3.py',
   'd:\\python\\lib\\site-packages\\cv2\\load_config_py3.py',
   'DATA'),
  ('cryptography-42.0.2.dist-info\\RECORD',
   'd:\\python\\lib\\site-packages\\cryptography-42.0.2.dist-info\\RECORD',
   'DATA'),
  ('cryptography-42.0.2.dist-info\\LICENSE.APACHE',
   'd:\\python\\lib\\site-packages\\cryptography-42.0.2.dist-info\\LICENSE.APACHE',
   'DATA'),
  ('certifi\\py.typed',
   'd:\\python\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('cryptography-42.0.2.dist-info\\INSTALLER',
   'd:\\python\\lib\\site-packages\\cryptography-42.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('importlib_metadata-4.13.0.dist-info\\RECORD',
   'd:\\python\\lib\\site-packages\\importlib_metadata-4.13.0.dist-info\\RECORD',
   'DATA'),
  ('cryptography-42.0.2.dist-info\\WHEEL',
   'd:\\python\\lib\\site-packages\\cryptography-42.0.2.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-42.0.2.dist-info\\top_level.txt',
   'd:\\python\\lib\\site-packages\\cryptography-42.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('cryptography-42.0.2.dist-info\\LICENSE.BSD',
   'd:\\python\\lib\\site-packages\\cryptography-42.0.2.dist-info\\LICENSE.BSD',
   'DATA'),
  ('cryptography-42.0.2.dist-info\\REQUESTED',
   'd:\\python\\lib\\site-packages\\cryptography-42.0.2.dist-info\\REQUESTED',
   'DATA'),
  ('importlib_metadata-4.13.0.dist-info\\INSTALLER',
   'd:\\python\\lib\\site-packages\\importlib_metadata-4.13.0.dist-info\\INSTALLER',
   'DATA'),
  ('cv2\\config.py', 'd:\\python\\lib\\site-packages\\cv2\\config.py', 'DATA'),
  ('cryptography-42.0.2.dist-info\\LICENSE',
   'd:\\python\\lib\\site-packages\\cryptography-42.0.2.dist-info\\LICENSE',
   'DATA'),
  ('importlib_metadata-4.13.0.dist-info\\WHEEL',
   'd:\\python\\lib\\site-packages\\importlib_metadata-4.13.0.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-42.0.2.dist-info\\METADATA',
   'd:\\python\\lib\\site-packages\\cryptography-42.0.2.dist-info\\METADATA',
   'DATA'),
  ('cv2\\config-3.py',
   'd:\\python\\lib\\site-packages\\cv2\\config-3.py',
   'DATA'),
  ('wheel-0.42.0.dist-info\\INSTALLER',
   'd:\\python\\lib\\site-packages\\wheel-0.42.0.dist-info\\INSTALLER',
   'DATA'),
  ('wheel-0.42.0.dist-info\\entry_points.txt',
   'd:\\python\\lib\\site-packages\\wheel-0.42.0.dist-info\\entry_points.txt',
   'DATA'),
  ('wheel-0.42.0.dist-info\\LICENSE.txt',
   'd:\\python\\lib\\site-packages\\wheel-0.42.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.42.0.dist-info\\RECORD',
   'd:\\python\\lib\\site-packages\\wheel-0.42.0.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.42.0.dist-info\\METADATA',
   'd:\\python\\lib\\site-packages\\wheel-0.42.0.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.42.0.dist-info\\WHEEL',
   'd:\\python\\lib\\site-packages\\wheel-0.42.0.dist-info\\WHEEL',
   'DATA'),
  ('cv2\\__init__.py',
   'd:\\python\\lib\\site-packages\\cv2\\__init__.py',
   'DATA'),
  ('cv2\\version.py',
   'd:\\python\\lib\\site-packages\\cv2\\version.py',
   'DATA'),
  ('cv2\\utils\\__init__.py',
   'd:\\python\\lib\\site-packages\\cv2\\utils\\__init__.py',
   'DATA'),
  ('cv2\\typing\\__init__.py',
   'd:\\python\\lib\\site-packages\\cv2\\typing\\__init__.py',
   'DATA'),
  ('cv2\\misc\\version.py',
   'd:\\python\\lib\\site-packages\\cv2\\misc\\version.py',
   'DATA'),
  ('cv2\\misc\\__init__.py',
   'd:\\python\\lib\\site-packages\\cv2\\misc\\__init__.py',
   'DATA'),
  ('cv2\\mat_wrapper\\__init__.py',
   'd:\\python\\lib\\site-packages\\cv2\\mat_wrapper\\__init__.py',
   'DATA'),
  ('cv2\\gapi\\__init__.py',
   'd:\\python\\lib\\site-packages\\cv2\\gapi\\__init__.py',
   'DATA'),
  ('cv2\\data\\__init__.py',
   'd:\\python\\lib\\site-packages\\cv2\\data\\__init__.py',
   'DATA')],)
