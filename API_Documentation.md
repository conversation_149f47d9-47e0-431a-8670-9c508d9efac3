# 智慧树查课API接口文档

## 接口概述

智慧树查课系统提供了统一的RESTful API接口，支持获取学分课和翻转课程列表。

**服务地址**: `http://localhost:7768` (开发环境)  
**API版本**: v1  
**数据格式**: JSON  

## 接口列表

### 1. 统一查课接口

#### 接口信息
- **URL**: `/api/v1/courses`
- **方法**: `POST`
- **描述**: 获取用户的课程列表，支持学分课、翻转课或全部课程

#### 请求参数

**Content-Type**: `application/json` 或 `application/x-www-form-urlencoded`

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| school | string | 是 | 学校名称 | "北京大学" |
| username | string | 是 | 用户名（手机号或学号） | "13800138000" 或 "**********" |
| password | string | 是 | 密码（至少6位） | "password123" |
| course_type | string | 否 | 课程类型 | "xfk", "fzk", "all" (默认: "all") |

**course_type 说明**:
- `xfk`: 仅获取学分课
- `fzk`: 仅获取翻转课  
- `all`: 获取全部课程（默认）

#### 请求示例

**JSON格式请求**:
```json
{
    "school": "北京大学",
    "username": "13800138000",
    "password": "password123",
    "course_type": "all"
}
```

**表单格式请求**:
```
school=北京大学&username=13800138000&password=password123&course_type=all
```

#### 响应格式

**成功响应** (HTTP 200):
```json
{
    "code": 1,
    "msg": "查询成功，共找到 5 门课程",
    "userName": "张三",
    "data": [
        {
            "name": "高等数学",
            "img": "https://example.com/course1.jpg",
            "id": "123456",
            "type": "学分课",
            "recruitId": "789012",
            "classId": "345678",
            "schoolId": "901234",
            "progress": 75
        },
        {
            "name": "计算机基础",
            "img": "https://example.com/course2.jpg", 
            "id": "654321",
            "type": "翻转课",
            "recruitId": "210987",
            "classId": "876543"
        }
    ]
}
```

**失败响应** (HTTP 200):
```json
{
    "code": -2,
    "msg": "账号密码错误",
    "data": []
}
```

#### 错误码说明

| 错误码 | 描述 |
|--------|------|
| 1 | 成功 |
| -1 | 参数错误 |
| -2 | 登录失败（账号密码错误、学校信息错误等） |
| -500 | 系统内部错误 |

### 2. 健康检查接口

#### 接口信息
- **URL**: `/api/v1/health`
- **方法**: `GET`
- **描述**: 检查服务运行状态

#### 响应示例
```json
{
    "status": "healthy",
    "service": "智慧树查课服务",
    "version": "1.0.0",
    "timestamp": **********
}
```

### 3. 兼容性接口

为了保持向后兼容，系统仍然支持原有的接口：

#### 学分课接口
- **URL**: `/zhs_xfk`
- **方法**: `GET` 或 `POST`
- **参数**: `school`, `user`, `pass`

#### 翻转课接口  
- **URL**: `/zhs_fzk`
- **方法**: `GET` 或 `POST`
- **参数**: `school`, `user`, `pass`

## cURL 使用示例

### 1. 获取全部课程

```bash
curl -X POST http://localhost:7768/api/v1/courses \
  -H "Content-Type: application/json" \
  -d '{
    "school": "北京大学",
    "username": "13800138000", 
    "password": "password123",
    "course_type": "all"
  }'
```

### 2. 仅获取学分课

```bash
curl -X POST http://localhost:7768/api/v1/courses \
  -H "Content-Type: application/json" \
  -d '{
    "school": "清华大学",
    "username": "**********",
    "password": "mypassword",
    "course_type": "xfk"
  }'
```

### 3. 仅获取翻转课

```bash
curl -X POST http://localhost:7768/api/v1/courses \
  -H "Content-Type: application/json" \
  -d '{
    "school": "复旦大学", 
    "username": "18912345678",
    "password": "123456789",
    "course_type": "fzk"
  }'
```

### 4. 使用表单数据格式

```bash
curl -X POST http://localhost:7768/api/v1/courses \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "school=北京大学&username=13800138000&password=password123&course_type=all"
```

### 5. 健康检查

```bash
curl -X GET http://localhost:7768/api/v1/health
```

### 6. 兼容性接口示例

```bash
# 学分课
curl "http://localhost:7768/zhs_xfk?school=北京大学&user=13800138000&pass=password123"

# 翻转课
curl "http://localhost:7768/zhs_fzk?school=北京大学&user=13800138000&pass=password123"
```

## 注意事项

### 1. 安全性
- 密码等敏感信息建议使用POST方法传输
- 生产环境建议使用HTTPS协议
- 建议实现API访问频率限制

### 2. 编码处理
- 支持UTF-8编码的中文学校名称
- 系统会自动处理编码转换问题

### 3. 错误处理
- 所有接口都会返回统一的错误格式
- 建议根据错误码进行相应的错误处理

### 4. 性能考虑
- 接口包含登录验证过程，响应时间可能较长（5-15秒）
- 建议实现客户端超时机制（建议30秒）
- 支持并发请求，但建议控制并发数量

### 5. 日志记录
- 系统会记录所有API调用日志
- 敏感信息（如密码）不会记录在日志中
- 用户名会进行部分脱敏处理

## 开发环境部署

### 1. 直接运行
```bash
python chake.py
```

### 2. 使用Gunicorn
```bash
gunicorn -c gunicorn_conf.py chake:app
```

### 3. 使用uWSGI
```bash
uwsgi --ini uwsgi.ini
```

## 更新日志

### v1.0.0 (2024-01-01)
- 新增统一查课接口 `/api/v1/courses`
- 新增健康检查接口 `/api/v1/health`
- 改进错误处理和参数验证
- 优化响应格式和日志记录
- 保持向后兼容性
