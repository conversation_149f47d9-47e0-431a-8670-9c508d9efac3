
from AES_PARAMS.AES_MAIN import *

class StatusKclist:
    def __init__(self, session):
        self.session = session
    def app_get_kclist(self,uuid):
        api = f"https://appstudent-api.zhihuishu.com/appstudent/gateway/t/v1/student/tutorialV2/getStudyingCourseListV6"
        list = []
        r = self.session.post(api,params={
            "jsonStr": "{\"isAll\":\"0\",\"page\":\"1\",\"pageSize\":\"500\"}",
            "osVersion": "2",
            "secretStr": "4T/BQfYHcPXI5gYgQOOCrdwFw47MTqlYj2I+J9sSUSHMqszU/d//f/7Bl3IH5QMdQftOvz01HPT0nS9rwsA1Pw==",
            "timeNote": "1515340800"
        }).json()
        if r["rt"]:
            for i in r["rt"]:
                recruitId = i['recruitId']
                courseId = i['courseId']
                courseName = i['courseName']
                img = i['courseImg']
                schoolid = i['schoolId']
                classId = i['classId']
                kcprocess = i['planProgress']
                list.append({"recruitId":recruitId,"courseId":courseId,"courseName":courseName,"img":img,"schoolid":schoolid,"classId":classId,"kcprocess":kcprocess})
        api = f"https://appstudent2c.zhihuishu.com/app_2c/studyv2/queryStudingListV2"
        strJson = str({"page":"1","pageSize":"500","uuid":f"{uuid}"})
        params = {
            "jsonStr": strJson,
            "osVersion": "2",
            "secretStr": AES_CBC_encrypt(key='59d78f063c204a99',iv='563216db1e1744c7',text=f"{strJson}2")
        }
        r = self.session.post(api,params=params).json()
        if r["rt"]:
            for i in r["rt"]:
                try:
                    if i['type'] == 5:
                        recruitId = i['recruitId']
                        courseId = i['id']
                        courseName = i['name']
                        img = i['img']
                        classId = i['classId']
                        list.append({"recruitId":recruitId,"courseId":courseId,"courseName":courseName,"img":img,"classId":classId})
                except:pass
        return list
    def get_app_kclist_fanzhuan(self,uuid,status=0):  #   翻转课
        list = []
        api = f"https://appstudent2c.zhihuishu.com/app_2c/studyv2/queryStudingListV2"
        strJson = str({"page":"1","pageSize":"500","uuid":f"{uuid}"})
        params = {
            "jsonStr": strJson,
            "osVersion": "2",
            "secretStr": AES_CBC_encrypt(key='59d78f063c204a99',iv='563216db1e1744c7',text=f"{strJson}2")
        }
        r = self.session.post(api,params=params).json()
        if r["rt"]:
            for i in r["rt"]:
                try:
                    recruitId = i['id']
                    courseId = i['courseId']
                    courseName = i['name']
                    img = i['img']
                    classId = i['classId']
                    list.append({"recruitId":recruitId,"courseId":courseId,"courseName":courseName,"img":img,"classId":classId})
                except:pass
        return list
