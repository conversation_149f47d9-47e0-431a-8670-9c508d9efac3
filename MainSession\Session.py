import random
import traceback
import requests
MAX_REQ = 5

def get_ip():
    # api = f"http://v2.api.juliangip.com/dynamic/getips?auto_white=1&num=1&pt=1&result_type=json&trade_no=1256002509496971&sign=47802d895e6e575e0e45fa1c3e548d33"
    # r = requests.get(api).json()
    # host = r["data"]["proxy_list"][0]
    # IP = {"http": f"http://{host}", "https": f"http://{host}"}
    return {}
class StartSession:
    def __init__(self, session):
        self.session = session
        self.ip = get_ip()
        self.session.headers = {
                "User-Agent": "android.zhihuishu.coma_zd"
        }
    def get(self, api, params=None, json=None, data=None, headers=None, allow_redirects=True):
        for i in range(MAX_REQ):
            try:
                r = self.session.get(api, params=params, json=json, data=data, headers=headers,
                                     allow_redirects=allow_redirects, proxies=self.ip)
                return r
            except:
                self.ip = get_ip()
                continue


    def post(self, api, params=None, json=None, data=None, headers=None, allow_redirects=True):
        for i in range(MAX_REQ):
            try:
                r = self.session.post(api, params=params, json=json, data=data, headers=headers,
                                     allow_redirects=allow_redirects, proxies=self.ip)
                return r
            except:
                traceback.print_exc()
                self.ip = get_ip()
                continue
    def cookie_get(self):
        return self.session.cookies.get_dict()

    def headers_update(self,data):
        self.session.headers.update(data)

    def headers_get(self):
        return self.session.headers