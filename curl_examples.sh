#!/bin/bash

# 智慧树查课API - cURL使用示例
# 使用前请确保服务已启动: python chake.py

# 服务器地址配置
BASE_URL="http://localhost:7768"

echo "=== 智慧树查课API cURL示例 ==="
echo "服务器地址: $BASE_URL"
echo ""

# 1. 健康检查
echo "1. 健康检查接口"
echo "curl -X GET $BASE_URL/api/v1/health"
echo ""
curl -X GET $BASE_URL/api/v1/health
echo -e "\n"

# 2. 获取全部课程 (JSON格式)
echo "2. 获取全部课程 (JSON格式)"
echo 'curl -X POST $BASE_URL/api/v1/courses \'
echo '  -H "Content-Type: application/json" \'
echo '  -d '"'"'{'
echo '    "school": "北京大学",'
echo '    "username": "13800138000",'
echo '    "password": "password123",'
echo '    "course_type": "all"'
echo '  }'"'"
echo ""

# 实际执行示例（请替换为真实的学校和账号信息）
# curl -X POST $BASE_URL/api/v1/courses \
#   -H "Content-Type: application/json" \
#   -d '{
#     "school": "北京大学",
#     "username": "13800138000", 
#     "password": "password123",
#     "course_type": "all"
#   }'

# 3. 仅获取学分课
echo "3. 仅获取学分课"
echo 'curl -X POST $BASE_URL/api/v1/courses \'
echo '  -H "Content-Type: application/json" \'
echo '  -d '"'"'{'
echo '    "school": "清华大学",'
echo '    "username": "**********",'
echo '    "password": "mypassword",'
echo '    "course_type": "xfk"'
echo '  }'"'"
echo ""

# 4. 仅获取翻转课
echo "4. 仅获取翻转课"
echo 'curl -X POST $BASE_URL/api/v1/courses \'
echo '  -H "Content-Type: application/json" \'
echo '  -d '"'"'{'
echo '    "school": "复旦大学",'
echo '    "username": "18912345678",'
echo '    "password": "123456789",'
echo '    "course_type": "fzk"'
echo '  }'"'"
echo ""

# 5. 使用表单数据格式
echo "5. 使用表单数据格式"
echo 'curl -X POST $BASE_URL/api/v1/courses \'
echo '  -H "Content-Type: application/x-www-form-urlencoded" \'
echo '  -d "school=北京大学&username=13800138000&password=password123&course_type=all"'
echo ""

# 6. 兼容性接口 - 学分课
echo "6. 兼容性接口 - 学分课"
echo 'curl "$BASE_URL/zhs_xfk?school=北京大学&user=13800138000&pass=password123"'
echo ""

# 7. 兼容性接口 - 翻转课
echo "7. 兼容性接口 - 翻转课"
echo 'curl "$BASE_URL/zhs_fzk?school=北京大学&user=13800138000&pass=password123"'
echo ""

# 8. 带详细输出的请求
echo "8. 带详细输出的请求示例"
echo 'curl -X POST $BASE_URL/api/v1/courses \'
echo '  -H "Content-Type: application/json" \'
echo '  -H "Accept: application/json" \'
echo '  -v \'
echo '  -d '"'"'{'
echo '    "school": "北京大学",'
echo '    "username": "13800138000",'
echo '    "password": "password123",'
echo '    "course_type": "all"'
echo '  }'"'"
echo ""

# 9. 保存响应到文件
echo "9. 保存响应到文件"
echo 'curl -X POST $BASE_URL/api/v1/courses \'
echo '  -H "Content-Type: application/json" \'
echo '  -d '"'"'{'
echo '    "school": "北京大学",'
echo '    "username": "13800138000",'
echo '    "password": "password123",'
echo '    "course_type": "all"'
echo '  }'"'" \'
echo '  -o course_response.json'
echo ""

# 10. 设置超时时间
echo "10. 设置超时时间（30秒）"
echo 'curl -X POST $BASE_URL/api/v1/courses \'
echo '  -H "Content-Type: application/json" \'
echo '  --connect-timeout 30 \'
echo '  --max-time 30 \'
echo '  -d '"'"'{'
echo '    "school": "北京大学",'
echo '    "username": "13800138000",'
echo '    "password": "password123",'
echo '    "course_type": "all"'
echo '  }'"'"
echo ""

# 11. 错误处理示例
echo "11. 错误处理示例 - 参数缺失"
echo 'curl -X POST $BASE_URL/api/v1/courses \'
echo '  -H "Content-Type: application/json" \'
echo '  -d '"'"'{'
echo '    "school": "",'
echo '    "username": "",'
echo '    "password": ""'
echo '  }'"'"
echo ""

# 12. 使用环境变量
echo "12. 使用环境变量的示例"
echo 'export ZHS_SCHOOL="北京大学"'
echo 'export ZHS_USERNAME="13800138000"'
echo 'export ZHS_PASSWORD="password123"'
echo ''
echo 'curl -X POST $BASE_URL/api/v1/courses \'
echo '  -H "Content-Type: application/json" \'
echo '  -d "{'
echo '    \"school\": \"$ZHS_SCHOOL\",'
echo '    \"username\": \"$ZHS_USERNAME\",'
echo '    \"password\": \"$ZHS_PASSWORD\",'
echo '    \"course_type\": \"all\"'
echo '  }"'
echo ""

# 13. 批量查询脚本示例
echo "13. 批量查询脚本示例"
cat << 'EOF'
# 创建批量查询脚本 batch_query.sh
#!/bin/bash

# 用户信息数组
declare -a users=(
    "北京大学,13800138000,password123"
    "清华大学,13800138001,password456"
    "复旦大学,13800138002,password789"
)

# 遍历用户进行查询
for user_info in "${users[@]}"; do
    IFS=',' read -r school username password <<< "$user_info"
    
    echo "查询用户: $username@$school"
    
    curl -X POST http://localhost:7768/api/v1/courses \
      -H "Content-Type: application/json" \
      -d "{
        \"school\": \"$school\",
        \"username\": \"$username\",
        \"password\": \"$password\",
        \"course_type\": \"all\"
      }" \
      -o "courses_${username}.json"
    
    echo "结果已保存到 courses_${username}.json"
    echo "---"
done
EOF
echo ""

# 14. PowerShell 示例 (Windows)
echo "14. PowerShell 示例 (Windows)"
cat << 'EOF'
# PowerShell 脚本示例
$headers = @{
    "Content-Type" = "application/json"
}

$body = @{
    school = "北京大学"
    username = "13800138000"
    password = "password123"
    course_type = "all"
} | ConvertTo-Json

$response = Invoke-RestMethod -Uri "http://localhost:7768/api/v1/courses" -Method Post -Headers $headers -Body $body

$response | ConvertTo-Json -Depth 10
EOF
echo ""

# 15. Python requests 示例
echo "15. Python requests 示例"
cat << 'EOF'
import requests
import json

# API配置
url = "http://localhost:7768/api/v1/courses"
headers = {"Content-Type": "application/json"}

# 请求数据
data = {
    "school": "北京大学",
    "username": "13800138000", 
    "password": "password123",
    "course_type": "all"
}

try:
    response = requests.post(url, headers=headers, json=data, timeout=30)
    result = response.json()
    
    if result["code"] == 1:
        print(f"查询成功: {result['msg']}")
        print(f"用户: {result['userName']}")
        print(f"课程数量: {len(result['data'])}")
        
        for course in result["data"]:
            print(f"- {course['name']} ({course['type']})")
    else:
        print(f"查询失败: {result['msg']}")
        
except requests.exceptions.RequestException as e:
    print(f"请求异常: {e}")
EOF
echo ""

echo "=== 使用说明 ==="
echo "1. 请将示例中的学校、用户名、密码替换为真实信息"
echo "2. 确保服务已启动在 $BASE_URL"
echo "3. 建议设置适当的超时时间（30秒）"
echo "4. 生产环境请使用HTTPS协议"
echo "5. 注意API调用频率，避免过于频繁的请求"
echo ""
echo "=== 常见问题 ==="
echo "Q: 接口响应时间较长？"
echo "A: 接口包含登录验证过程，正常响应时间为5-15秒"
echo ""
echo "Q: 中文学校名称乱码？"
echo "A: 确保使用UTF-8编码，系统会自动处理编码转换"
echo ""
echo "Q: 登录失败？"
echo "A: 检查学校名称、用户名、密码是否正确，注意区分手机号和学号登录"
