import json
import re
import math
import random
import time
import traceback
from loguru import logger
import ddddocr
from requests.adapters import HTTPAdapter
import execjs
import requests
import numpy as np
# from execjs import _runner_sources
from YiDun.gap import get_gap
from YiDun import path as file_path
def proxies_ip():
    api = f"http://**********:5656/get_ip"
    r = requests.get(api).json()
    host = r['ip']
    IP = {"http": host, "https": host}
    return IP
    
def create_session_with_retry(retries=10, backoff_factor=0.1, status_forcelist=(500, 502, 504), session=None):
    """
    创建一个带有重连策略的Session对象，并禁用连接持久性。

    参数：
    - retries: 最大重试次数
    - backoff_factor: 重试之间的时间间隔因子，用于指数退避算法，控制重试时等待时间的增加速率
    - status_forcelist: 遇到哪些状态码时应该进行重试
    - session: 可选，如果提供了一个已存在的Session对象，则将重试策略应用于该对象，否则创建一个新的Session对象
    """
    if session is None:
        session = requests.Session()

    session.keep_alive = False  # 禁用连接持久性

    retry_strategy = requests.packages.urllib3.util.retry.Retry(
        total=retries,
        backoff_factor=backoff_factor,
        status_forcelist=status_forcelist
    )
    adapter = requests.adapters.HTTPAdapter(max_retries=retry_strategy)
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    return session

def get_track(space):
    """
    获取轨迹
    :param space:
    :return:
    """
    x = [0, 0]
    y = [0, 0, 0]
    z = [0]
    count = np.linspace(-math.pi / 2, math.pi / 2, random.randrange(20, 30))
    func = list(map(math.sin, count))
    nx = [i + 1 for i in func]
    add = random.randrange(10, 15)
    sadd = space + add
    x.extend(list(map(lambda x: x * (sadd / 2), nx)))
    x.extend(np.linspace(sadd, space, 3 if add > 12 else 2))
    x = [math.floor(i) for i in x]
    for i in range(len(x) - 2):
        if y[-1] < 30:
            y.append(y[-1] + random.choice([0, 0, 1, 1, 2, 2, 1, 2, 0, 0, 3, 3]))
        else:
            y.append(y[-1] + random.choice([0, 0, -1, -1, -2, -2, -1, -2, 0, 0, -3, -3]))
    for i in range(len(x) - 1):
        z.append((z[-1] // 100 * 100) + 100 + random.choice([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2]))
    return list(map(list, zip(x, y, z)))


def get_ctx(path):
    """
    获取js对象
    :param path: js文件路径
    :return:
    """
    # with open(path, 'r', encoding='utf-8') as f:
    #     content = f.read()
    content =open(path, 'r', encoding='utf-8').read()
    ctx = execjs.compile(content)
    return ctx


def get_fp_callback():
    """
    获取fp、callback参数
    :return: fp, callback
    """
    ctx = get_ctx(file_path.CAPTCHA_FP_JS_PATH)
    fp = ctx.call('get_fp')
    callback = ctx.call('get_callback')
    return fp, callback


def get_secure_captcha(validate, fp, zoneId):
    """
    获取加密后的验证码
    :param validate: 验证成功返回的 validate
    :param fp: fingerprint
    :param zoneId: 一般是CN31
    :return: 加密后的验证码
    """
    ctx = get_ctx(file_path.CAPTCHA_SC_JS_PATH)
    return ctx.call('getSecureCaptcha', validate, fp, zoneId)


class crypto_params:
    def __init__(self):
        self.ctx = get_ctx(file_path.CAPTCHA_CB_JS_PATH)

    def get_cb(self):
        return self.ctx.call('cb')

    def get_data(self, token, trace, left):
        return self.ctx.call('get_data', token, trace, left)


class yidun:
    def __init__(self, captcha_id='', captcha_data=None, actoken=False):
        """
        获取网易易盾的validate
        :param captcha_id: 网易易盾的CAPTCHA_ID
        :param captcha_data: 验证码参数设置
        :param actoken: 是否启用actoken
        """
        self.captcha_id = captcha_id
        self.captcha_data = {
            'v': 'af2952a4',
            'version': '2.24.0',
            'type': '2',
            'referer': 'https://passport.zhihuishu.com/login'
        }
        if captcha_data:
            self.captcha_data.update(captcha_data)
        self.result = None
        self.secure_captcha = None
        self.validate = None
        self.counter = 0
        self.actoken = actoken
        self.fp = ""
        self.session = requests.session()#create_session_with_retry(retries=10, status_forcelist=(500, 502, 504, 443, 400))
        self.ip = {}

    def get_validate(self, actoken=None):
        headers = {
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Cache-Control': 'no-cache',
            'Host': 'c.dun.163yun.com',
            'Referer': '',
            'Pragma': 'no-cache',
            'Proxy-Connection': 'keep-alive',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36'
        }
        # 获取fp和callback
        self.fp, callback = get_fp_callback()

        crypto_param = crypto_params()
        cb = crypto_param.get_cb()

        data = {
            "id": self.captcha_id,
            "fp": self.fp,
            "https": "true",
            "type": self.captcha_data.get('type', '2'),
            "version": self.captcha_data.get('version', '2.21.5'),
            "dpr": "1",
            "dev": "1",
            "cb": cb,
            "ipv6": "false",
            "runEnv": "10",
            "group": "",
            "scene": "",
            "width": "320",
            "token": "",
            "referer": "https://base2.zhihuishu.com/ableCaptcha/case/ableCaptchaAppv2.html",
            "callback": callback
        }
        try:
            r = self.session.get('https://c.dun.163.com/api/v3/get', params=data, headers=headers,proxies=self.ip)
        except:
            self.ip = proxies_ip()
            return self.get_validate()
        if r.status_code != 200:
            raise Exception("请求get接口错误！请检查captcha_id是否正确或网络是否可用。")
        data_ = json.loads(re.findall('.*?\((.*?)\);', r.text)[0])
        token = data_['data']['token']

        img1 = self.session.get(data_.get('data').get('front')[0]).content
        img2 = self.session.get(data_.get('data').get('bg')[0]).content
        # distance = get_gap(img1, img2) + 5
        #
        # trace = get_track(distance)
        # left = trace[-1][0] - 10
        det = ddddocr.DdddOcr(det=False, ocr=False, show_ad=False)
        left = det.slide_match(img1, img2, simple_target=True)['target'][0]
        distance = get_gap(img1, img2) + 5
        logger.success(f"滑块坐标X轴:{left}")
        trace = get_track(distance)
        data_ = crypto_param.get_data(token, trace, left)
        cb = crypto_param.get_cb()
        # 生成actoken
        actoken = "undefined"
        # 提交验证部分
        get_data = {
            "id": self.captcha_id,
            "token": token,
            "acToken": actoken,
            "data": data_,
            "width": "320",
            "type": self.captcha_data.get('type', '2'),
            "version": self.captcha_data.get('version', '2.21.5'),
            "cb": cb,
            "extraData": "",
            "runEnv": "10",
            "referer": "",
            "callback": "__JSONP_48mk47t_1"
        }
        try:
            r = self.session.get('https://c.dun.163.com/api/v3/check', headers=headers, params=get_data,proxies=self.ip)
            data = r.text[18:-2]
            self.result = json.loads(data)
            if self.result.get("data", {}).get("result", False):
                self.validate = self.result['data']['validate']
                self.secure_captcha = get_secure_captcha(self.validate, self.fp, self.result['data']['zoneId'])
                self.token = self.result['data']['token']
                self.img_data = {"token":self.token,"validate":self.secure_captcha}
            else:
                self.ip = proxies_ip()
                logger.error(f"验证失败！{self.result}")
                self.get_validate(actoken)
        except:
            self.ip = proxies_ip()
            traceback.print_exc()
            self.get_validate(actoken)


