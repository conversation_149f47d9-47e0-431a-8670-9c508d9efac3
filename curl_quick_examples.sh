#!/bin/bash

# 智慧树查课API - 快速cURL配置
# 使用前请确保服务已启动: python chake.py

echo "=== 智慧树查课API 快速cURL配置 ==="
echo ""

# 基础配置
BASE_URL="http://localhost:7768"
echo "服务器地址: $BASE_URL"
echo ""

# 用户信息配置（请替换为真实信息）
SCHOOL="北京大学"
USER="your_username"    # 手机号或学号
PASS="your_password"

echo "请将以下变量替换为真实信息："
echo "SCHOOL=\"$SCHOOL\""
echo "USER=\"$USER\""
echo "PASS=\"$PASS\""
echo ""

echo "=== 基础cURL命令 ==="
echo ""

echo "1. 查询学分课程（GET方式）："
echo "curl \"$BASE_URL/zhs_xfk?school=$SCHOOL&user=$USER&pass=$PASS\""
echo ""

echo "2. 查询翻转课程（GET方式）："
echo "curl \"$BASE_URL/zhs_fzk?school=$SCHOOL&user=$USER&pass=$PASS\""
echo ""

echo "3. 查询学分课程（POST方式）："
echo "curl -X POST \"$BASE_URL/zhs_xfk?school=$SCHOOL&user=$USER&pass=$PASS\""
echo ""

echo "4. 查询翻转课程（POST方式）："
echo "curl -X POST \"$BASE_URL/zhs_fzk?school=$SCHOOL&user=$USER&pass=$PASS\""
echo ""

echo "=== 带超时设置的命令 ==="
echo ""

echo "5. 学分课程查询（60秒超时）："
echo "curl --connect-timeout 30 --max-time 60 \"$BASE_URL/zhs_xfk?school=$SCHOOL&user=$USER&pass=$PASS\""
echo ""

echo "6. 翻转课程查询（60秒超时）："
echo "curl --connect-timeout 30 --max-time 60 \"$BASE_URL/zhs_fzk?school=$SCHOOL&user=$USER&pass=$PASS\""
echo ""

echo "=== 保存结果到文件 ==="
echo ""

echo "7. 保存学分课程查询结果："
echo "curl \"$BASE_URL/zhs_xfk?school=$SCHOOL&user=$USER&pass=$PASS\" -o xfk_courses.json"
echo ""

echo "8. 保存翻转课程查询结果："
echo "curl \"$BASE_URL/zhs_fzk?school=$SCHOOL&user=$USER&pass=$PASS\" -o fzk_courses.json"
echo ""

echo "=== 详细输出模式 ==="
echo ""

echo "9. 详细输出学分课程查询："
echo "curl -v \"$BASE_URL/zhs_xfk?school=$SCHOOL&user=$USER&pass=$PASS\""
echo ""

echo "10. 详细输出翻转课程查询："
echo "curl -v \"$BASE_URL/zhs_fzk?school=$SCHOOL&user=$USER&pass=$PASS\""
echo ""

echo "=== 实际可执行的示例 ==="
echo ""

# 如果用户设置了真实信息，可以取消注释以下行进行实际测试
echo "# 取消注释以下行进行实际测试（请先设置真实的用户信息）："
echo ""
echo "# 测试学分课程查询"
echo "# curl \"$BASE_URL/zhs_xfk?school=北京大学&user=13800138000&pass=password123\""
echo ""
echo "# 测试翻转课程查询"
echo "# curl \"$BASE_URL/zhs_fzk?school=北京大学&user=13800138000&pass=password123\""
echo ""

echo "=== 使用说明 ==="
echo ""
echo "1. 将上面的 SCHOOL、USER、PASS 替换为真实信息"
echo "2. 复制相应的 curl 命令到终端执行"
echo "3. 建议设置超时时间，因为接口响应时间较长（10-30秒）"
echo "4. 支持中文学校名称，系统会自动处理编码"
echo "5. 用户名支持手机号（11位数字）和学号两种格式"
echo ""

echo "=== 常见错误处理 ==="
echo ""
echo "如果遇到错误，请检查："
echo "- 服务是否已启动（python chake.py）"
echo "- 学校名称是否正确（建议使用完整名称）"
echo "- 用户名和密码是否正确"
echo "- 网络连接是否正常"
echo ""

echo "=== 响应格式说明 ==="
echo ""
echo "成功响应示例："
echo '{'
echo '  "code": 1,'
echo '  "msg": "查询成功",'
echo '  "userName": "用户真实姓名",'
echo '  "data": ['
echo '    {'
echo '      "name": "课程名称",'
echo '      "img": "课程图片URL",'
echo '      "id": "课程ID"'
echo '    }'
echo '  ]'
echo '}'
echo ""

echo "失败响应示例："
echo '{'
echo '  "code": -2,'
echo '  "msg": "账号密码错误"'
echo '}'
echo ""

echo "=== 批量查询示例 ==="
echo ""
echo "如需批量查询多个用户，可以创建如下脚本："
echo ""
cat << 'EOF'
#!/bin/bash
# 批量查询脚本示例

users=(
    "北京大学,13800138000,password1"
    "清华大学,2021001001,password2"
)

for user_info in "${users[@]}"; do
    IFS=',' read -r school username password <<< "$user_info"
    echo "查询用户: $username@$school"
    
    curl "http://localhost:7768/zhs_xfk?school=$school&user=$username&pass=$password" \
      -o "xfk_${username}.json"
    
    curl "http://localhost:7768/zhs_fzk?school=$school&user=$username&pass=$password" \
      -o "fzk_${username}.json"
    
    echo "结果已保存"
    echo "---"
done
EOF

echo ""
echo "=== 结束 ==="
echo "更多详细信息请查看 README_API.md 文件"
