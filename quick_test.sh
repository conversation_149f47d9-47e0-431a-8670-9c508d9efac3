#!/bin/bash

# 智慧树查课API快速测试脚本
# 使用方法: ./quick_test.sh [学校名称] [用户名] [密码] [课程类型]

# 默认配置
DEFAULT_SERVER="http://localhost:7768"
DEFAULT_SCHOOL="北京大学"
DEFAULT_USERNAME="13800138000"
DEFAULT_PASSWORD="password123"
DEFAULT_COURSE_TYPE="all"

# 获取参数
SERVER=${1:-$DEFAULT_SERVER}
SCHOOL=${2:-$DEFAULT_SCHOOL}
USERNAME=${3:-$DEFAULT_USERNAME}
PASSWORD=${4:-$DEFAULT_PASSWORD}
COURSE_TYPE=${5:-$DEFAULT_COURSE_TYPE}

echo "=== 智慧树查课API快速测试 ==="
echo "服务器: $SERVER"
echo "学校: $SCHOOL"
echo "用户名: $USERNAME"
echo "课程类型: $COURSE_TYPE"
echo ""

# 1. 健康检查
echo "1. 检查服务状态..."
health_response=$(curl -s -X GET "$SERVER/api/v1/health")
if [ $? -eq 0 ]; then
    echo "✓ 服务运行正常"
    echo "响应: $health_response"
else
    echo "✗ 服务连接失败，请检查服务是否启动"
    exit 1
fi
echo ""

# 2. 查课测试
echo "2. 执行查课请求..."
echo "请求URL: $SERVER/api/v1/courses"
echo "请求数据: {\"school\":\"$SCHOOL\",\"username\":\"$USERNAME\",\"password\":\"[隐藏]\",\"course_type\":\"$COURSE_TYPE\"}"
echo ""

# 执行查课请求
response=$(curl -s -X POST "$SERVER/api/v1/courses" \
  -H "Content-Type: application/json" \
  -d "{
    \"school\": \"$SCHOOL\",
    \"username\": \"$USERNAME\",
    \"password\": \"$PASSWORD\",
    \"course_type\": \"$COURSE_TYPE\"
  }")

# 检查响应
if [ $? -eq 0 ]; then
    echo "✓ 请求发送成功"
    
    # 解析响应（简单的JSON解析）
    code=$(echo "$response" | grep -o '"code":[^,]*' | cut -d':' -f2 | tr -d ' ')
    msg=$(echo "$response" | grep -o '"msg":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    
    if [ "$code" = "1" ]; then
        echo "✓ 查课成功"
        echo "消息: $msg"
        
        # 尝试提取课程数量
        data_count=$(echo "$response" | grep -o '"data":\[[^]]*\]' | grep -o '{[^}]*}' | wc -l)
        echo "课程数量: $data_count"
        
        # 保存完整响应到文件
        echo "$response" | python -m json.tool > "course_result_$(date +%Y%m%d_%H%M%S).json" 2>/dev/null
        if [ $? -eq 0 ]; then
            echo "✓ 完整响应已保存到 course_result_$(date +%Y%m%d_%H%M%S).json"
        fi
        
    else
        echo "✗ 查课失败"
        echo "错误码: $code"
        echo "错误信息: $msg"
    fi
    
    echo ""
    echo "完整响应:"
    echo "$response" | python -m json.tool 2>/dev/null || echo "$response"
    
else
    echo "✗ 请求发送失败"
    exit 1
fi

echo ""
echo "=== 测试完成 ==="
