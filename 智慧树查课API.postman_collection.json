{"info": {"_postman_id": "zhihui<PERSON>-chake-api", "name": "智慧树查课API", "description": "智慧树查课系统API接口集合，包含统一查课接口和兼容性接口", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "健康检查", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/health", "host": ["{{base_url}}"], "path": ["api", "v1", "health"]}, "description": "检查服务运行状态"}, "response": []}, {"name": "统一查课接口 - 全部课程", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"school\": \"{{school}}\",\n    \"username\": \"{{username}}\",\n    \"password\": \"{{password}}\",\n    \"course_type\": \"all\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/courses", "host": ["{{base_url}}"], "path": ["api", "v1", "courses"]}, "description": "获取用户的全部课程列表（学分课+翻转课）"}, "response": []}, {"name": "统一查课接口 - 仅学分课", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"school\": \"{{school}}\",\n    \"username\": \"{{username}}\",\n    \"password\": \"{{password}}\",\n    \"course_type\": \"xfk\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/courses", "host": ["{{base_url}}"], "path": ["api", "v1", "courses"]}, "description": "仅获取学分课程列表"}, "response": []}, {"name": "统一查课接口 - 仅翻转课", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"school\": \"{{school}}\",\n    \"username\": \"{{username}}\",\n    \"password\": \"{{password}}\",\n    \"course_type\": \"fzk\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/courses", "host": ["{{base_url}}"], "path": ["api", "v1", "courses"]}, "description": "仅获取翻转课程列表"}, "response": []}, {"name": "统一查课接口 - 表单格式", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "school", "value": "{{school}}", "type": "text"}, {"key": "username", "value": "{{username}}", "type": "text"}, {"key": "password", "value": "{{password}}", "type": "text"}, {"key": "course_type", "value": "all", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/v1/courses", "host": ["{{base_url}}"], "path": ["api", "v1", "courses"]}, "description": "使用表单数据格式的查课请求"}, "response": []}, {"name": "兼容接口 - 学分课", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/zhs_xfk?school={{school}}&user={{username}}&pass={{password}}", "host": ["{{base_url}}"], "path": ["zhs_xfk"], "query": [{"key": "school", "value": "{{school}}"}, {"key": "user", "value": "{{username}}"}, {"key": "pass", "value": "{{password}}"}]}, "description": "兼容性接口 - 获取学分课程"}, "response": []}, {"name": "兼容接口 - 翻转课", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/zhs_fzk?school={{school}}&user={{username}}&pass={{password}}", "host": ["{{base_url}}"], "path": ["zhs_fzk"], "query": [{"key": "school", "value": "{{school}}"}, {"key": "user", "value": "{{username}}"}, {"key": "pass", "value": "{{password}}"}]}, "description": "兼容性接口 - 获取翻转课程"}, "response": []}, {"name": "参数验证测试 - 缺失参数", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"school\": \"\",\n    \"username\": \"\",\n    \"password\": \"\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/courses", "host": ["{{base_url}}"], "path": ["api", "v1", "courses"]}, "description": "测试参数验证功能 - 提交空参数"}, "response": []}, {"name": "参数验证测试 - 错误课程类型", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"school\": \"{{school}}\",\n    \"username\": \"{{username}}\",\n    \"password\": \"{{password}}\",\n    \"course_type\": \"invalid_type\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/courses", "host": ["{{base_url}}"], "path": ["api", "v1", "courses"]}, "description": "测试参数验证功能 - 错误的课程类型"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// 预请求脚本", "console.log('发送请求到:', pm.request.url.toString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// 通用测试脚本", "pm.test('响应状态码为200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('响应格式为JSON', function () {", "    pm.response.to.be.json;", "});", "", "pm.test('响应包含必要字段', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('code');", "    pm.expect(jsonData).to.have.property('msg');", "    pm.expect(jsonData).to.have.property('data');", "});", "", "// 记录响应时间", "console.log('响应时间:', pm.response.responseTime + 'ms');"]}}], "variable": [{"key": "base_url", "value": "http://localhost:7768", "type": "string"}, {"key": "school", "value": "北京大学", "type": "string"}, {"key": "username", "value": "13800138000", "type": "string"}, {"key": "password", "value": "password123", "type": "string"}]}