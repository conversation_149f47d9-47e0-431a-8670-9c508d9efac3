@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM 智慧树查课API快速测试脚本 (Windows版本)
REM 使用方法: quick_test.bat [学校名称] [用户名] [密码] [课程类型]

REM 默认配置
set DEFAULT_SERVER=http://localhost:7768
set DEFAULT_SCHOOL=北京大学
set DEFAULT_USERNAME=13800138000
set DEFAULT_PASSWORD=password123
set DEFAULT_COURSE_TYPE=all

REM 获取参数
if "%1"=="" (set SERVER=%DEFAULT_SERVER%) else (set SERVER=%1)
if "%2"=="" (set SCHOOL=%DEFAULT_SCHOOL%) else (set SCHOOL=%2)
if "%3"=="" (set USERNAME=%DEFAULT_USERNAME%) else (set USERNAME=%3)
if "%4"=="" (set PASSWORD=%DEFAULT_PASSWORD%) else (set PASSWORD=%4)
if "%5"=="" (set COURSE_TYPE=%DEFAULT_COURSE_TYPE%) else (set COURSE_TYPE=%5)

echo === 智慧树查课API快速测试 ===
echo 服务器: %SERVER%
echo 学校: %SCHOOL%
echo 用户名: %USERNAME%
echo 课程类型: %COURSE_TYPE%
echo.

REM 1. 健康检查
echo 1. 检查服务状态...
curl -s -X GET "%SERVER%/api/v1/health" > health_response.tmp
if %errorlevel% equ 0 (
    echo ✓ 服务运行正常
    echo 响应: 
    type health_response.tmp
    del health_response.tmp
) else (
    echo ✗ 服务连接失败，请检查服务是否启动
    if exist health_response.tmp del health_response.tmp
    pause
    exit /b 1
)
echo.

REM 2. 查课测试
echo 2. 执行查课请求...
echo 请求URL: %SERVER%/api/v1/courses
echo 请求数据: {"school":"%SCHOOL%","username":"%USERNAME%","password":"[隐藏]","course_type":"%COURSE_TYPE%"}
echo.

REM 创建临时JSON文件
echo { > request.tmp
echo   "school": "%SCHOOL%", >> request.tmp
echo   "username": "%USERNAME%", >> request.tmp
echo   "password": "%PASSWORD%", >> request.tmp
echo   "course_type": "%COURSE_TYPE%" >> request.tmp
echo } >> request.tmp

REM 执行查课请求
curl -s -X POST "%SERVER%/api/v1/courses" -H "Content-Type: application/json" -d @request.tmp > response.tmp

if %errorlevel% equ 0 (
    echo ✓ 请求发送成功
    
    REM 检查响应中的code字段
    findstr /C:"\"code\":1" response.tmp >nul
    if !errorlevel! equ 0 (
        echo ✓ 查课成功
        
        REM 保存响应到文件
        set timestamp=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
        set timestamp=!timestamp: =0!
        copy response.tmp "course_result_!timestamp!.json" >nul
        echo ✓ 完整响应已保存到 course_result_!timestamp!.json
    ) else (
        findstr /C:"\"code\":-" response.tmp >nul
        if !errorlevel! equ 0 (
            echo ✗ 查课失败
            echo 检查响应中的错误信息:
        ) else (
            echo ? 响应格式异常
        )
    )
    
    echo.
    echo 完整响应:
    type response.tmp
    
) else (
    echo ✗ 请求发送失败
    if exist request.tmp del request.tmp
    if exist response.tmp del response.tmp
    pause
    exit /b 1
)

REM 清理临时文件
if exist request.tmp del request.tmp
if exist response.tmp del response.tmp

echo.
echo === 测试完成 ===
pause
