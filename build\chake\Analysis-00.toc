(['E:\\智慧树爬题库\\智慧树查课\\chake.py'],
 ['E:\\智慧树爬题库\\智慧树查课'],
 ['codecs'],
 ['d:\\python\\lib\\site-packages\\pywebio\\platform\\pyinstaller',
  'd:\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\stdhooks',
  'd:\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\stdhooks\\__pycache__',
  'd:\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks',
  'd:\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks\\__pycache__',
  'd:\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks'],
 {},
 [],
 [],
 False,
 False,
 False,
 {},
 [],
 [('AES_PARAMS\\AES_MAIN.py',
   'E:\\智慧树爬题库\\智慧树查课\\AES_PARAMS\\AES_MAIN.py',
   'DATA'),
  ('AES_PARAMS\\__pycache__\\AES_MAIN.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\AES_PARAMS\\__pycache__\\AES_MAIN.cpython-37.pyc',
   'DATA'),
  ('MainSession\\Session.py',
   'E:\\智慧树爬题库\\智慧树查课\\MainSession\\Session.py',
   'DATA'),
  ('MainSession\\__pycache__\\Session.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\MainSession\\__pycache__\\Session.cpython-37.pyc',
   'DATA'),
  ('UserList\\UserKclist.py',
   'E:\\智慧树爬题库\\智慧树查课\\UserList\\UserKclist.py',
   'DATA'),
  ('UserList\\__pycache__\\UserKclist.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\UserList\\__pycache__\\UserKclist.cpython-37.pyc',
   'DATA'),
  ('UserLogin\\Login.py', 'E:\\智慧树爬题库\\智慧树查课\\UserLogin\\Login.py', 'DATA'),
  ('UserLogin\\__pycache__\\Login.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\UserLogin\\__pycache__\\Login.cpython-37.pyc',
   'DATA'),
  ('UserLogin\\__pycache__\\phone.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\UserLogin\\__pycache__\\phone.cpython-37.pyc',
   'DATA'),
  ('UserLogin\\__pycache__\\school.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\UserLogin\\__pycache__\\school.cpython-37.pyc',
   'DATA'),
  ('UserLogin\\phone.py', 'E:\\智慧树爬题库\\智慧树查课\\UserLogin\\phone.py', 'DATA'),
  ('UserLogin\\school.py', 'E:\\智慧树爬题库\\智慧树查课\\UserLogin\\school.py', 'DATA'),
  ('UserLogin\\转换cookie.py',
   'E:\\智慧树爬题库\\智慧树查课\\UserLogin\\转换cookie.py',
   'DATA'),
  ('YiDun\\__init__.py', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\__init__.py', 'DATA'),
  ('YiDun\\__pycache__\\__init__.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\YiDun\\__pycache__\\__init__.cpython-37.pyc',
   'DATA'),
  ('YiDun\\__pycache__\\captcha.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\YiDun\\__pycache__\\captcha.cpython-37.pyc',
   'DATA'),
  ('YiDun\\__pycache__\\cptcha_dian.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\YiDun\\__pycache__\\cptcha_dian.cpython-37.pyc',
   'DATA'),
  ('YiDun\\__pycache__\\gap.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\YiDun\\__pycache__\\gap.cpython-37.pyc',
   'DATA'),
  ('YiDun\\__pycache__\\path.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\YiDun\\__pycache__\\path.cpython-37.pyc',
   'DATA'),
  ('YiDun\\__pycache__\\打码.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\YiDun\\__pycache__\\打码.cpython-37.pyc',
   'DATA'),
  ('YiDun\\captcha.py', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\captcha.py', 'DATA'),
  ('YiDun\\cptcha_dian.py', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\cptcha_dian.py', 'DATA'),
  ('YiDun\\gap.py', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\gap.py', 'DATA'),
  ('YiDun\\js\\actoken.js', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\js\\actoken.js', 'DATA'),
  ('YiDun\\js\\cb.js', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\js\\cb.js', 'DATA'),
  ('YiDun\\js\\fp.js', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\js\\fp.js', 'DATA'),
  ('YiDun\\js\\secureCaptcha.js',
   'E:\\智慧树爬题库\\智慧树查课\\YiDun\\js\\secureCaptcha.js',
   'DATA'),
  ('YiDun\\path.py', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\path.py', 'DATA'),
  ('YiDun\\打码.py', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\打码.py', 'DATA')],
 '3.7.9 (tags/v3.7.9:13c94747c7, Aug 17 2020, 18:58:18) [MSC v.1900 64 bit '
 '(AMD64)]',
 [('pyi_rth_pkgutil',
   'd:\\python\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'd:\\python\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'd:\\python\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'd:\\python\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'd:\\python\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('chake', 'E:\\智慧树爬题库\\智慧树查课\\chake.py', 'PYSOURCE')],
 [('pkg_resources',
   'd:\\python\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('packaging.version',
   'd:\\python\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('typing', 'd:\\python\\lib\\typing.py', 'PYMODULE'),
  ('contextlib', 'd:\\python\\lib\\contextlib.py', 'PYMODULE'),
  ('packaging.utils',
   'd:\\python\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.tags',
   'd:\\python\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('subprocess', 'd:\\python\\lib\\subprocess.py', 'PYMODULE'),
  ('selectors', 'd:\\python\\lib\\selectors.py', 'PYMODULE'),
  ('threading', 'd:\\python\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'd:\\python\\lib\\_threading_local.py', 'PYMODULE'),
  ('signal', 'd:\\python\\lib\\signal.py', 'PYMODULE'),
  ('logging', 'd:\\python\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'd:\\python\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'd:\\python\\lib\\pprint.py', 'PYMODULE'),
  ('_compat_pickle', 'd:\\python\\lib\\_compat_pickle.py', 'PYMODULE'),
  ('struct', 'd:\\python\\lib\\struct.py', 'PYMODULE'),
  ('string', 'd:\\python\\lib\\string.py', 'PYMODULE'),
  ('packaging.specifiers',
   'd:\\python\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'd:\\python\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.metadata',
   'd:\\python\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('typing_extensions',
   'd:\\python\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('email.policy', 'd:\\python\\lib\\email\\policy.py', 'PYMODULE'),
  ('email', 'd:\\python\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._header_value_parser',
   'd:\\python\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._encoded_words',
   'd:\\python\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'd:\\python\\lib\\base64.py', 'PYMODULE'),
  ('getopt', 'd:\\python\\lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'd:\\python\\lib\\gettext.py', 'PYMODULE'),
  ('copy', 'd:\\python\\lib\\copy.py', 'PYMODULE'),
  ('email.charset', 'd:\\python\\lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'd:\\python\\lib\\email\\encoders.py', 'PYMODULE'),
  ('quopri', 'd:\\python\\lib\\quopri.py', 'PYMODULE'),
  ('email.quoprimime', 'd:\\python\\lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.base64mime', 'd:\\python\\lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.errors', 'd:\\python\\lib\\email\\errors.py', 'PYMODULE'),
  ('email.contentmanager',
   'd:\\python\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'd:\\python\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.utils', 'd:\\python\\lib\\email\\utils.py', 'PYMODULE'),
  ('email._parseaddr', 'd:\\python\\lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('calendar', 'd:\\python\\lib\\calendar.py', 'PYMODULE'),
  ('argparse', 'd:\\python\\lib\\argparse.py', 'PYMODULE'),
  ('datetime', 'd:\\python\\lib\\datetime.py', 'PYMODULE'),
  ('_strptime', 'd:\\python\\lib\\_strptime.py', 'PYMODULE'),
  ('socket', 'd:\\python\\lib\\socket.py', 'PYMODULE'),
  ('random', 'd:\\python\\lib\\random.py', 'PYMODULE'),
  ('bisect', 'd:\\python\\lib\\bisect.py', 'PYMODULE'),
  ('hashlib', 'd:\\python\\lib\\hashlib.py', 'PYMODULE'),
  ('email._policybase', 'd:\\python\\lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.message', 'd:\\python\\lib\\email\\message.py', 'PYMODULE'),
  ('email.iterators', 'd:\\python\\lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'd:\\python\\lib\\email\\generator.py', 'PYMODULE'),
  ('uu', 'd:\\python\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'd:\\python\\lib\\optparse.py', 'PYMODULE'),
  ('email.header', 'd:\\python\\lib\\email\\header.py', 'PYMODULE'),
  ('email.feedparser', 'd:\\python\\lib\\email\\feedparser.py', 'PYMODULE'),
  ('packaging.markers',
   'd:\\python\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'd:\\python\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('dataclasses', 'd:\\python\\lib\\dataclasses.py', 'PYMODULE'),
  ('packaging._structures',
   'd:\\python\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   'd:\\python\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('ast', 'd:\\python\\lib\\ast.py', 'PYMODULE'),
  ('packaging._musllinux',
   'd:\\python\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'd:\\python\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('ctypes', 'd:\\python\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes.wintypes', 'd:\\python\\lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('ctypes._endian', 'd:\\python\\lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('packaging._elffile',
   'd:\\python\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging',
   'd:\\python\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources._vendor.typing_extensions',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.windows',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('__future__', 'd:\\python\\lib\\__future__.py', 'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.version',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.unix',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('configparser', 'd:\\python\\lib\\configparser.py', 'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.macos',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.api',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.android',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.__main__',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__main__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.metadata',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\metadata.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._tokenizer',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._parser',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._elffile',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('queue', 'd:\\python\\lib\\queue.py', 'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('importlib.resources',
   'd:\\python\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.abc', 'd:\\python\\lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib._bootstrap_external',
   'd:\\python\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'd:\\python\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('shutil', 'd:\\python\\lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'd:\\python\\lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'd:\\python\\lib\\gzip.py', 'PYMODULE'),
  ('_compression', 'd:\\python\\lib\\_compression.py', 'PYMODULE'),
  ('lzma', 'd:\\python\\lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'd:\\python\\lib\\bz2.py', 'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'd:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('sysconfig', 'd:\\python\\lib\\sysconfig.py', 'PYMODULE'),
  ('pkg_resources.extern',
   'd:\\python\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('importlib.util', 'd:\\python\\lib\\importlib\\util.py', 'PYMODULE'),
  ('importlib.machinery',
   'd:\\python\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('imp', 'd:\\python\\lib\\imp.py', 'PYMODULE'),
  ('importlib', 'd:\\python\\lib\\importlib\\__init__.py', 'PYMODULE'),
  ('inspect', 'd:\\python\\lib\\inspect.py', 'PYMODULE'),
  ('dis', 'd:\\python\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'd:\\python\\lib\\opcode.py', 'PYMODULE'),
  ('textwrap', 'd:\\python\\lib\\textwrap.py', 'PYMODULE'),
  ('tempfile', 'd:\\python\\lib\\tempfile.py', 'PYMODULE'),
  ('email.parser', 'd:\\python\\lib\\email\\parser.py', 'PYMODULE'),
  ('plistlib', 'd:\\python\\lib\\plistlib.py', 'PYMODULE'),
  ('xml.parsers.expat', 'd:\\python\\lib\\xml\\parsers\\expat.py', 'PYMODULE'),
  ('xml.parsers', 'd:\\python\\lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml', 'd:\\python\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'd:\\python\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils', 'd:\\python\\lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax', 'd:\\python\\lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax.handler', 'd:\\python\\lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'd:\\python\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader', 'd:\\python\\lib\\xml\\sax\\xmlreader.py', 'PYMODULE'),
  ('platform', 'd:\\python\\lib\\platform.py', 'PYMODULE'),
  ('pkgutil', 'd:\\python\\lib\\pkgutil.py', 'PYMODULE'),
  ('zipfile', 'd:\\python\\lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'd:\\python\\lib\\py_compile.py', 'PYMODULE'),
  ('_distutils_hack',
   'd:\\python\\lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('setuptools',
   'd:\\python\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'd:\\python\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'd:\\python\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('_osx_support', 'd:\\python\\lib\\_osx_support.py', 'PYMODULE'),
  ('distutils.log', 'd:\\python\\lib\\distutils\\log.py', 'PYMODULE'),
  ('distutils', 'd:\\python\\lib\\distutils\\__init__.py', 'PYMODULE'),
  ('distutils.archive_util',
   'd:\\python\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.dir_util', 'd:\\python\\lib\\distutils\\dir_util.py', 'PYMODULE'),
  ('distutils.file_util',
   'd:\\python\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.dep_util', 'd:\\python\\lib\\distutils\\dep_util.py', 'PYMODULE'),
  ('distutils.spawn', 'd:\\python\\lib\\distutils\\spawn.py', 'PYMODULE'),
  ('distutils.debug', 'd:\\python\\lib\\distutils\\debug.py', 'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('shlex', 'd:\\python\\lib\\shlex.py', 'PYMODULE'),
  ('setuptools._distutils.text_file',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\py39compat.py',
   'PYMODULE'),
  ('setuptools._distutils.py38compat',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\msvccompiler.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'd:\\python\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'd:\\python\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'd:\\python\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'd:\\python\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('distutils.version', 'd:\\python\\lib\\distutils\\version.py', 'PYMODULE'),
  ('distutils.command',
   'd:\\python\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.cmd', 'd:\\python\\lib\\distutils\\cmd.py', 'PYMODULE'),
  ('distutils.dist', 'd:\\python\\lib\\distutils\\dist.py', 'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('cgi', 'd:\\python\\lib\\cgi.py', 'PYMODULE'),
  ('html', 'd:\\python\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'd:\\python\\lib\\html\\entities.py', 'PYMODULE'),
  ('setuptools._distutils.command.upload',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\upload.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('glob', 'd:\\python\\lib\\glob.py', 'PYMODULE'),
  ('setuptools._distutils.command.register',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\register.py',
   'PYMODULE'),
  ('getpass', 'd:\\python\\lib\\getpass.py', 'PYMODULE'),
  ('setuptools._distutils.command.py37compat',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\py37compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('site', 'd:\\python\\lib\\site.py', 'PYMODULE'),
  ('rlcompleter', 'd:\\python\\lib\\rlcompleter.py', 'PYMODULE'),
  ('readline', 'd:\\python\\lib\\site-packages\\readline.py', 'PYMODULE'),
  ('pyreadline.console',
   'd:\\python\\lib\\site-packages\\pyreadline\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline.console.console',
   'd:\\python\\lib\\site-packages\\pyreadline\\console\\console.py',
   'PYMODULE'),
  ('pyreadline.console.event',
   'd:\\python\\lib\\site-packages\\pyreadline\\console\\event.py',
   'PYMODULE'),
  ('ctypes.util', 'd:\\python\\lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes._aix', 'd:\\python\\lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes.macholib.dyld',
   'd:\\python\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'd:\\python\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'd:\\python\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'd:\\python\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('pyreadline.console.ansi',
   'd:\\python\\lib\\site-packages\\pyreadline\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline.keysyms',
   'd:\\python\\lib\\site-packages\\pyreadline\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline.keysyms.keysyms',
   'd:\\python\\lib\\site-packages\\pyreadline\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline.keysyms.common',
   'd:\\python\\lib\\site-packages\\pyreadline\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline.keysyms.ironpython_keysyms',
   'd:\\python\\lib\\site-packages\\pyreadline\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline.keysyms.winconstants',
   'd:\\python\\lib\\site-packages\\pyreadline\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline.logger',
   'd:\\python\\lib\\site-packages\\pyreadline\\logger.py',
   'PYMODULE'),
  ('logging.handlers', 'd:\\python\\lib\\logging\\handlers.py', 'PYMODULE'),
  ('http.client', 'd:\\python\\lib\\http\\client.py', 'PYMODULE'),
  ('ssl', 'd:\\python\\lib\\ssl.py', 'PYMODULE'),
  ('http', 'd:\\python\\lib\\http\\__init__.py', 'PYMODULE'),
  ('smtplib', 'd:\\python\\lib\\smtplib.py', 'PYMODULE'),
  ('hmac', 'd:\\python\\lib\\hmac.py', 'PYMODULE'),
  ('pyreadline.unicode_helper',
   'd:\\python\\lib\\site-packages\\pyreadline\\unicode_helper.py',
   'PYMODULE'),
  ('pyreadline.py3k_compat',
   'd:\\python\\lib\\site-packages\\pyreadline\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline.console.ironpython_console',
   'd:\\python\\lib\\site-packages\\pyreadline\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline',
   'd:\\python\\lib\\site-packages\\pyreadline\\__init__.py',
   'PYMODULE'),
  ('pyreadline.release',
   'd:\\python\\lib\\site-packages\\pyreadline\\release.py',
   'PYMODULE'),
  ('pyreadline.modes',
   'd:\\python\\lib\\site-packages\\pyreadline\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline.modes.vi',
   'd:\\python\\lib\\site-packages\\pyreadline\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline.lineeditor.history',
   'd:\\python\\lib\\site-packages\\pyreadline\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline.lineeditor.lineobj',
   'd:\\python\\lib\\site-packages\\pyreadline\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline.lineeditor.wordmatcher',
   'd:\\python\\lib\\site-packages\\pyreadline\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline.modes.notemacs',
   'd:\\python\\lib\\site-packages\\pyreadline\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline.modes.emacs',
   'd:\\python\\lib\\site-packages\\pyreadline\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline.modes.basemode',
   'd:\\python\\lib\\site-packages\\pyreadline\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline.error',
   'd:\\python\\lib\\site-packages\\pyreadline\\error.py',
   'PYMODULE'),
  ('pyreadline.lineeditor',
   'd:\\python\\lib\\site-packages\\pyreadline\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline.clipboard',
   'd:\\python\\lib\\site-packages\\pyreadline\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline.clipboard.win32_clipboard',
   'd:\\python\\lib\\site-packages\\pyreadline\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline.clipboard.no_clipboard',
   'd:\\python\\lib\\site-packages\\pyreadline\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline.clipboard.ironpython_clipboard',
   'd:\\python\\lib\\site-packages\\pyreadline\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline.rlmain',
   'd:\\python\\lib\\site-packages\\pyreadline\\rlmain.py',
   'PYMODULE'),
  ('_sitebuiltins', 'd:\\python\\lib\\_sitebuiltins.py', 'PYMODULE'),
  ('pydoc', 'd:\\python\\lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'd:\\python\\lib\\webbrowser.py', 'PYMODULE'),
  ('http.server', 'd:\\python\\lib\\http\\server.py', 'PYMODULE'),
  ('socketserver', 'd:\\python\\lib\\socketserver.py', 'PYMODULE'),
  ('mimetypes', 'd:\\python\\lib\\mimetypes.py', 'PYMODULE'),
  ('pydoc_data.topics', 'd:\\python\\lib\\pydoc_data\\topics.py', 'PYMODULE'),
  ('pydoc_data', 'd:\\python\\lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('tty', 'd:\\python\\lib\\tty.py', 'PYMODULE'),
  ('setuptools._distutils.command.config',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('concurrent.futures',
   'd:\\python\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'd:\\python\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'd:\\python\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'd:\\python\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'd:\\python\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'd:\\python\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.semaphore_tracker',
   'd:\\python\\lib\\multiprocessing\\semaphore_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'd:\\python\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy', 'd:\\python\\lib\\runpy.py', 'PYMODULE'),
  ('multiprocessing.process',
   'd:\\python\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'd:\\python\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'd:\\python\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'd:\\python\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'd:\\python\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'd:\\python\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'd:\\python\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'd:\\python\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'd:\\python\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'd:\\python\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'd:\\python\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'd:\\python\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'd:\\python\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'd:\\python\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'd:\\python\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'd:\\python\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client', 'd:\\python\\lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc', 'd:\\python\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('decimal', 'd:\\python\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'd:\\python\\lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'd:\\python\\lib\\contextvars.py', 'PYMODULE'),
  ('numbers', 'd:\\python\\lib\\numbers.py', 'PYMODULE'),
  ('multiprocessing',
   'd:\\python\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'd:\\python\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent', 'd:\\python\\lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('unittest.mock', 'd:\\python\\lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest', 'd:\\python\\lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest.signals', 'd:\\python\\lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.main', 'd:\\python\\lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.runner', 'd:\\python\\lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.loader', 'd:\\python\\lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.suite', 'd:\\python\\lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.case', 'd:\\python\\lib\\unittest\\case.py', 'PYMODULE'),
  ('difflib', 'd:\\python\\lib\\difflib.py', 'PYMODULE'),
  ('unittest.result', 'd:\\python\\lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.util', 'd:\\python\\lib\\unittest\\util.py', 'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\_collections.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'd:\\python\\lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\zipp.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.metadata',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools.extern',
   'd:\\python\\lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._py39compat',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_py39compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('csv', 'd:\\python\\lib\\csv.py', 'PYMODULE'),
  ('setuptools._vendor',
   'd:\\python\\lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'd:\\python\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('json', 'd:\\python\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'd:\\python\\lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'd:\\python\\lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'd:\\python\\lib\\json\\scanner.py', 'PYMODULE'),
  ('distutils.command.build_ext',
   'd:\\python\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'd:\\python\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.extension',
   'd:\\python\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('setuptools.logging',
   'd:\\python\\lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'd:\\python\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('distutils.filelist', 'd:\\python\\lib\\distutils\\filelist.py', 'PYMODULE'),
  ('setuptools.discovery',
   'd:\\python\\lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.errors',
   'd:\\python\\lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.depends',
   'd:\\python\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'd:\\python\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.dist',
   'd:\\python\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.installer',
   'd:\\python\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'd:\\python\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'd:\\python\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._path',
   'd:\\python\\lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'd:\\python\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.glob',
   'd:\\python\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'd:\\python\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'd:\\python\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'd:\\python\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'd:\\python\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'd:\\python\\lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build',
   'd:\\python\\lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'd:\\python\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'd:\\python\\lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('importlib_metadata',
   'd:\\python\\lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'd:\\python\\lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'd:\\python\\lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'd:\\python\\lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'd:\\python\\lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._py39compat',
   'd:\\python\\lib\\site-packages\\importlib_metadata\\_py39compat.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'd:\\python\\lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'd:\\python\\lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'd:\\python\\lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('zipp', 'd:\\python\\lib\\site-packages\\zipp\\__init__.py', 'PYMODULE'),
  ('zipp.py310compat',
   'd:\\python\\lib\\site-packages\\zipp\\py310compat.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'd:\\python\\lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'd:\\python\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'd:\\python\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'd:\\python\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'd:\\python\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'd:\\python\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'd:\\python\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'd:\\python\\lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'd:\\python\\lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'd:\\python\\lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config',
   'd:\\python\\lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.command',
   'd:\\python\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'd:\\python\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'd:\\python\\lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'd:\\python\\lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'd:\\python\\lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'd:\\python\\lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'd:\\python\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.extension',
   'd:\\python\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.version',
   'd:\\python\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'd:\\python\\lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('distutils.util', 'd:\\python\\lib\\distutils\\util.py', 'PYMODULE'),
  ('distutils.errors', 'd:\\python\\lib\\distutils\\errors.py', 'PYMODULE'),
  ('distutils.core', 'd:\\python\\lib\\distutils\\core.py', 'PYMODULE'),
  ('distutils.config', 'd:\\python\\lib\\distutils\\config.py', 'PYMODULE'),
  ('_distutils_hack.override',
   'd:\\python\\lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_py_abc', 'd:\\python\\lib\\_py_abc.py', 'PYMODULE'),
  ('stringprep', 'd:\\python\\lib\\stringprep.py', 'PYMODULE'),
  ('tracemalloc', 'd:\\python\\lib\\tracemalloc.py', 'PYMODULE'),
  ('nturl2path', 'd:\\python\\lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'd:\\python\\lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'd:\\python\\lib\\netrc.py', 'PYMODULE'),
  ('http.cookiejar', 'd:\\python\\lib\\http\\cookiejar.py', 'PYMODULE'),
  ('UserList.UserKclist',
   'E:\\智慧树爬题库\\智慧树查课\\UserList\\UserKclist.py',
   'PYMODULE'),
  ('UserList', '-', 'PYMODULE'),
  ('AES_PARAMS.AES_MAIN',
   'E:\\智慧树爬题库\\智慧树查课\\AES_PARAMS\\AES_MAIN.py',
   'PYMODULE'),
  ('AES_PARAMS', '-', 'PYMODULE'),
  ('Crypto.Util.Padding',
   'd:\\python\\lib\\site-packages\\Crypto\\Util\\Padding.py',
   'PYMODULE'),
  ('Crypto.Util',
   'd:\\python\\lib\\site-packages\\Crypto\\Util\\__init__.py',
   'PYMODULE'),
  ('Crypto.Util._cpu_features',
   'd:\\python\\lib\\site-packages\\Crypto\\Util\\_cpu_features.py',
   'PYMODULE'),
  ('Crypto.Util._raw_api',
   'd:\\python\\lib\\site-packages\\Crypto\\Util\\_raw_api.py',
   'PYMODULE'),
  ('cffi', 'd:\\python\\lib\\site-packages\\cffi\\__init__.py', 'PYMODULE'),
  ('cffi.error', 'd:\\python\\lib\\site-packages\\cffi\\error.py', 'PYMODULE'),
  ('cffi.api', 'd:\\python\\lib\\site-packages\\cffi\\api.py', 'PYMODULE'),
  ('cffi.recompiler',
   'd:\\python\\lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'd:\\python\\lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.verifier',
   'd:\\python\\lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cffi.lock', 'd:\\python\\lib\\site-packages\\cffi\\lock.py', 'PYMODULE'),
  ('_dummy_thread', 'd:\\python\\lib\\_dummy_thread.py', 'PYMODULE'),
  ('cffi.pkgconfig',
   'd:\\python\\lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'd:\\python\\lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'd:\\python\\lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'd:\\python\\lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.cparser',
   'd:\\python\\lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'd:\\python\\lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'd:\\python\\lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pycparser',
   'd:\\python\\lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'd:\\python\\lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'd:\\python\\lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'd:\\python\\lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'd:\\python\\lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'd:\\python\\lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'd:\\python\\lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.ply',
   'd:\\python\\lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'd:\\python\\lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'd:\\python\\lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.model', 'd:\\python\\lib\\site-packages\\cffi\\model.py', 'PYMODULE'),
  ('Crypto.Util._file_system',
   'd:\\python\\lib\\site-packages\\Crypto\\Util\\_file_system.py',
   'PYMODULE'),
  ('Crypto', 'd:\\python\\lib\\site-packages\\Crypto\\__init__.py', 'PYMODULE'),
  ('Crypto.Util.py3compat',
   'd:\\python\\lib\\site-packages\\Crypto\\Util\\py3compat.py',
   'PYMODULE'),
  ('Crypto.Cipher.AES',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\AES.py',
   'PYMODULE'),
  ('Crypto.Random',
   'd:\\python\\lib\\site-packages\\Crypto\\Random\\__init__.py',
   'PYMODULE'),
  ('Crypto.Cipher',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\__init__.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ocb',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_mode_ocb.py',
   'PYMODULE'),
  ('Crypto.Hash.BLAKE2s',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\BLAKE2s.py',
   'PYMODULE'),
  ('Crypto.Hash',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\__init__.py',
   'PYMODULE'),
  ('Crypto.Hash.HMAC',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\HMAC.py',
   'PYMODULE'),
  ('Crypto.Hash.MD5',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\MD5.py',
   'PYMODULE'),
  ('Crypto.Hash.CMAC',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\CMAC.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_512',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\SHA3_512.py',
   'PYMODULE'),
  ('Crypto.Hash.keccak',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\keccak.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_384',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\SHA3_384.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_256',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\SHA3_256.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_224',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\SHA3_224.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA512',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\SHA512.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA384',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\SHA384.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA256',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\SHA256.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA224',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\SHA224.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA1',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\SHA1.py',
   'PYMODULE'),
  ('Crypto.Util.strxor',
   'd:\\python\\lib\\site-packages\\Crypto\\Util\\strxor.py',
   'PYMODULE'),
  ('Crypto.Util.number',
   'd:\\python\\lib\\site-packages\\Crypto\\Util\\number.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_gcm',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_mode_gcm.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_siv',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_mode_siv.py',
   'PYMODULE'),
  ('Crypto.Protocol.KDF',
   'd:\\python\\lib\\site-packages\\Crypto\\Protocol\\KDF.py',
   'PYMODULE'),
  ('Crypto.Protocol',
   'd:\\python\\lib\\site-packages\\Crypto\\Protocol\\__init__.py',
   'PYMODULE'),
  ('Crypto.Cipher._EKSBlowfish',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_EKSBlowfish.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_eax',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_mode_eax.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ccm',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_mode_ccm.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_openpgp',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_mode_openpgp.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ctr',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_mode_ctr.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ofb',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_mode_ofb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_cfb',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_mode_cfb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_cbc',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_mode_cbc.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ecb',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_mode_ecb.py',
   'PYMODULE'),
  ('UserLogin.Login', 'E:\\智慧树爬题库\\智慧树查课\\UserLogin\\Login.py', 'PYMODULE'),
  ('UserLogin', '-', 'PYMODULE'),
  ('YiDun.cptcha_dian', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\cptcha_dian.py', 'PYMODULE'),
  ('YiDun.path', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\path.py', 'PYMODULE'),
  ('YiDun', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\__init__.py', 'PYMODULE'),
  ('numpy', 'd:\\python\\lib\\site-packages\\numpy\\__init__.py', 'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'd:\\python\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'd:\\python\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'd:\\python\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'd:\\python\\lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.distutils.cpuinfo',
   'd:\\python\\lib\\site-packages\\numpy\\distutils\\cpuinfo.py',
   'PYMODULE'),
  ('numpy.distutils',
   'd:\\python\\lib\\site-packages\\numpy\\distutils\\__init__.py',
   'PYMODULE'),
  ('doctest', 'd:\\python\\lib\\doctest.py', 'PYMODULE'),
  ('pdb', 'd:\\python\\lib\\pdb.py', 'PYMODULE'),
  ('code', 'd:\\python\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'd:\\python\\lib\\codeop.py', 'PYMODULE'),
  ('bdb', 'd:\\python\\lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'd:\\python\\lib\\cmd.py', 'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'd:\\python\\lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'd:\\python\\lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'd:\\python\\lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'd:\\python\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'd:\\python\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('psutil', 'd:\\python\\lib\\site-packages\\psutil\\__init__.py', 'PYMODULE'),
  ('psutil._pswindows',
   'd:\\python\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._compat',
   'd:\\python\\lib\\site-packages\\psutil\\_compat.py',
   'PYMODULE'),
  ('dummy_threading', 'd:\\python\\lib\\dummy_threading.py', 'PYMODULE'),
  ('psutil._common',
   'd:\\python\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'd:\\python\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'd:\\python\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'd:\\python\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'd:\\python\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'd:\\python\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'd:\\python\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'd:\\python\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'd:\\python\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'd:\\python\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'd:\\python\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'd:\\python\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'd:\\python\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'd:\\python\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'd:\\python\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'd:\\python\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'd:\\python\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'd:\\python\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'd:\\python\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'd:\\python\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.ma',
   'd:\\python\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'd:\\python\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'd:\\python\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'd:\\python\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'd:\\python\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'd:\\python\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'd:\\python\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'd:\\python\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'd:\\python\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'd:\\python\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.random',
   'd:\\python\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'd:\\python\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'd:\\python\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'd:\\python\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'd:\\python\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'd:\\python\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'd:\\python\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'd:\\python\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'd:\\python\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'd:\\python\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'd:\\python\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.fft',
   'd:\\python\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'd:\\python\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'd:\\python\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   'd:\\python\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'd:\\python\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.lib',
   'd:\\python\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'd:\\python\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'd:\\python\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'd:\\python\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'd:\\python\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'd:\\python\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.core.records',
   'd:\\python\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'd:\\python\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'd:\\python\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'd:\\python\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'd:\\python\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'd:\\python\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'd:\\python\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'd:\\python\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'd:\\python\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'd:\\python\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'd:\\python\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'd:\\python\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.machar',
   'd:\\python\\lib\\site-packages\\numpy\\core\\machar.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'd:\\python\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'd:\\python\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.version',
   'd:\\python\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy.compat',
   'd:\\python\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'd:\\python\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'd:\\python\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'd:\\python\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'd:\\python\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'd:\\python\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'd:\\python\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'd:\\python\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'd:\\python\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._version',
   'd:\\python\\lib\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy.__config__',
   'd:\\python\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._globals',
   'd:\\python\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('execjs', 'd:\\python\\lib\\site-packages\\execjs\\__init__.py', 'PYMODULE'),
  ('execjs._abstract_runtime',
   'd:\\python\\lib\\site-packages\\execjs\\_abstract_runtime.py',
   'PYMODULE'),
  ('six', 'd:\\python\\lib\\site-packages\\six.py', 'PYMODULE'),
  ('execjs._external_runtime',
   'd:\\python\\lib\\site-packages\\execjs\\_external_runtime.py',
   'PYMODULE'),
  ('execjs._misc',
   'd:\\python\\lib\\site-packages\\execjs\\_misc.py',
   'PYMODULE'),
  ('execjs._abstract_runtime_context',
   'd:\\python\\lib\\site-packages\\execjs\\_abstract_runtime_context.py',
   'PYMODULE'),
  ('execjs._runner_sources',
   'd:\\python\\lib\\site-packages\\execjs\\_runner_sources.py',
   'PYMODULE'),
  ('execjs._json2',
   'd:\\python\\lib\\site-packages\\execjs\\_json2.py',
   'PYMODULE'),
  ('execjs._runtimes',
   'd:\\python\\lib\\site-packages\\execjs\\_runtimes.py',
   'PYMODULE'),
  ('execjs._pyv8runtime',
   'd:\\python\\lib\\site-packages\\execjs\\_pyv8runtime.py',
   'PYMODULE'),
  ('execjs.runtime_names',
   'd:\\python\\lib\\site-packages\\execjs\\runtime_names.py',
   'PYMODULE'),
  ('execjs._exceptions',
   'd:\\python\\lib\\site-packages\\execjs\\_exceptions.py',
   'PYMODULE'),
  ('requests.adapters',
   'd:\\python\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'd:\\python\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'd:\\python\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'd:\\python\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('idna', 'd:\\python\\lib\\site-packages\\idna\\__init__.py', 'PYMODULE'),
  ('idna.intranges',
   'd:\\python\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core', 'd:\\python\\lib\\site-packages\\idna\\core.py', 'PYMODULE'),
  ('idna.uts46data',
   'd:\\python\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'd:\\python\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.package_data',
   'd:\\python\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'd:\\python\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'd:\\python\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'd:\\python\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'd:\\python\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'd:\\python\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'd:\\python\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.utils',
   'd:\\python\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('ipaddress', 'd:\\python\\lib\\ipaddress.py', 'PYMODULE'),
  ('cryptography.x509',
   'd:\\python\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'd:\\python\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'd:\\python\\lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('cryptography',
   'd:\\python\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'd:\\python\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('urllib3',
   'd:\\python\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'd:\\python\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'd:\\python\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'd:\\python\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.response',
   'd:\\python\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'd:\\python\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'd:\\python\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'd:\\python\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3._version',
   'd:\\python\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3._collections',
   'd:\\python\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'd:\\python\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'd:\\python\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'd:\\python\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'd:\\python\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'd:\\python\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'd:\\python\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'd:\\python\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'd:\\python\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'd:\\python\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3.connection',
   'd:\\python\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'd:\\python\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('socks', 'd:\\python\\lib\\site-packages\\socks.py', 'PYMODULE'),
  ('requests.utils',
   'd:\\python\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'd:\\python\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.__version__',
   'd:\\python\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.certs',
   'd:\\python\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'd:\\python\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'd:\\python\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('requests.structures',
   'd:\\python\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.models',
   'd:\\python\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.status_codes',
   'd:\\python\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.hooks',
   'd:\\python\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.exceptions',
   'd:\\python\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.cookies',
   'd:\\python\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.compat',
   'd:\\python\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies', 'd:\\python\\lib\\http\\cookies.py', 'PYMODULE'),
  ('charset_normalizer',
   'd:\\python\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'd:\\python\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'd:\\python\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'd:\\python\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.assets',
   'd:\\python\\lib\\site-packages\\charset_normalizer\\assets\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'd:\\python\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'd:\\python\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'd:\\python\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'd:\\python\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'd:\\python\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'd:\\python\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util',
   'd:\\python\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'd:\\python\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'd:\\python\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('YiDun.captcha', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\captcha.py', 'PYMODULE'),
  ('YiDun.gap', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\gap.py', 'PYMODULE'),
  ('ddddocr',
   'd:\\python\\lib\\site-packages\\ddddocr\\__init__.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'd:\\python\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.Image', 'd:\\python\\lib\\site-packages\\PIL\\Image.py', 'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'd:\\python\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'd:\\python\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   'd:\\python\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'd:\\python\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'd:\\python\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsStubImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\FitsStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'd:\\python\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('fractions', 'd:\\python\\lib\\fractions.py', 'PYMODULE'),
  ('PIL.ImageShow',
   'd:\\python\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'd:\\python\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'd:\\python\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys', 'd:\\python\\lib\\colorsys.py', 'PYMODULE'),
  ('PIL.ImageFilter',
   'd:\\python\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'd:\\python\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'd:\\python\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'd:\\python\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'd:\\python\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'd:\\python\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'd:\\python\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'd:\\python\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'd:\\python\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'd:\\python\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL._util', 'd:\\python\\lib\\site-packages\\PIL\\_util.py', 'PYMODULE'),
  ('PIL._deprecate',
   'd:\\python\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._binary',
   'd:\\python\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'd:\\python\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'd:\\python\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'd:\\python\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL', 'd:\\python\\lib\\site-packages\\PIL\\__init__.py', 'PYMODULE'),
  ('PIL._version',
   'd:\\python\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('onnxruntime',
   'd:\\python\\lib\\site-packages\\onnxruntime\\__init__.py',
   'PYMODULE'),
  ('onnxruntime.capi.training',
   'd:\\python\\lib\\site-packages\\onnxruntime\\capi\\training\\__init__.py',
   'PYMODULE'),
  ('onnxruntime.capi.onnxruntime_inference_collection',
   'd:\\python\\lib\\site-packages\\onnxruntime\\capi\\onnxruntime_inference_collection.py',
   'PYMODULE'),
  ('onnxruntime.capi.onnxruntime_validation',
   'd:\\python\\lib\\site-packages\\onnxruntime\\capi\\onnxruntime_validation.py',
   'PYMODULE'),
  ('onnxruntime.capi.onnxruntime_collect_build_info',
   'd:\\python\\lib\\site-packages\\onnxruntime\\capi\\onnxruntime_collect_build_info.py',
   'PYMODULE'),
  ('onnxruntime.capi',
   'd:\\python\\lib\\site-packages\\onnxruntime\\capi\\__init__.py',
   'PYMODULE'),
  ('onnxruntime.capi.version_info',
   'd:\\python\\lib\\site-packages\\onnxruntime\\capi\\version_info.py',
   'PYMODULE'),
  ('onnxruntime.capi._ld_preload',
   'd:\\python\\lib\\site-packages\\onnxruntime\\capi\\_ld_preload.py',
   'PYMODULE'),
  ('onnxruntime.capi._pybind_state',
   'd:\\python\\lib\\site-packages\\onnxruntime\\capi\\_pybind_state.py',
   'PYMODULE'),
  ('UserLogin.school', 'E:\\智慧树爬题库\\智慧树查课\\UserLogin\\school.py', 'PYMODULE'),
  ('UserLogin.phone', 'E:\\智慧树爬题库\\智慧树查课\\UserLogin\\phone.py', 'PYMODULE'),
  ('YiDun.打码', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\打码.py', 'PYMODULE'),
  ('MainSession.Session',
   'E:\\智慧树爬题库\\智慧树查课\\MainSession\\Session.py',
   'PYMODULE'),
  ('MainSession', '-', 'PYMODULE'),
  ('loguru', 'd:\\python\\lib\\site-packages\\loguru\\__init__.py', 'PYMODULE'),
  ('loguru._logger',
   'd:\\python\\lib\\site-packages\\loguru\\_logger.py',
   'PYMODULE'),
  ('loguru._simple_sinks',
   'd:\\python\\lib\\site-packages\\loguru\\_simple_sinks.py',
   'PYMODULE'),
  ('asyncio', 'd:\\python\\lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'd:\\python\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log', 'd:\\python\\lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.windows_events',
   'd:\\python\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'd:\\python\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'd:\\python\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'd:\\python\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'd:\\python\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.subprocess', 'd:\\python\\lib\\asyncio\\subprocess.py', 'PYMODULE'),
  ('asyncio.streams', 'd:\\python\\lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.queues', 'd:\\python\\lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'd:\\python\\lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.tasks', 'd:\\python\\lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.locks', 'd:\\python\\lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.base_tasks', 'd:\\python\\lib\\asyncio\\base_tasks.py', 'PYMODULE'),
  ('asyncio.sslproto', 'd:\\python\\lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.transports', 'd:\\python\\lib\\asyncio\\transports.py', 'PYMODULE'),
  ('asyncio.base_events',
   'd:\\python\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.protocols', 'd:\\python\\lib\\asyncio\\protocols.py', 'PYMODULE'),
  ('asyncio.futures', 'd:\\python\\lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.events', 'd:\\python\\lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.coroutines', 'd:\\python\\lib\\asyncio\\coroutines.py', 'PYMODULE'),
  ('asyncio.base_futures',
   'd:\\python\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'd:\\python\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants', 'd:\\python\\lib\\asyncio\\constants.py', 'PYMODULE'),
  ('loguru._recattrs',
   'd:\\python\\lib\\site-packages\\loguru\\_recattrs.py',
   'PYMODULE'),
  ('loguru._locks_machinery',
   'd:\\python\\lib\\site-packages\\loguru\\_locks_machinery.py',
   'PYMODULE'),
  ('loguru._handler',
   'd:\\python\\lib\\site-packages\\loguru\\_handler.py',
   'PYMODULE'),
  ('loguru._get_frame',
   'd:\\python\\lib\\site-packages\\loguru\\_get_frame.py',
   'PYMODULE'),
  ('loguru._file_sink',
   'd:\\python\\lib\\site-packages\\loguru\\_file_sink.py',
   'PYMODULE'),
  ('loguru._ctime_functions',
   'd:\\python\\lib\\site-packages\\loguru\\_ctime_functions.py',
   'PYMODULE'),
  ('win32_setctime',
   'd:\\python\\lib\\site-packages\\win32_setctime.py',
   'PYMODULE'),
  ('loguru._error_interceptor',
   'd:\\python\\lib\\site-packages\\loguru\\_error_interceptor.py',
   'PYMODULE'),
  ('loguru._datetime',
   'd:\\python\\lib\\site-packages\\loguru\\_datetime.py',
   'PYMODULE'),
  ('loguru._contextvars',
   'd:\\python\\lib\\site-packages\\loguru\\_contextvars.py',
   'PYMODULE'),
  ('loguru._colorizer',
   'd:\\python\\lib\\site-packages\\loguru\\_colorizer.py',
   'PYMODULE'),
  ('loguru._better_exceptions',
   'd:\\python\\lib\\site-packages\\loguru\\_better_exceptions.py',
   'PYMODULE'),
  ('exceptiongroup',
   'd:\\python\\lib\\site-packages\\exceptiongroup\\__init__.py',
   'PYMODULE'),
  ('exceptiongroup._suppress',
   'd:\\python\\lib\\site-packages\\exceptiongroup\\_suppress.py',
   'PYMODULE'),
  ('exceptiongroup._formatting',
   'd:\\python\\lib\\site-packages\\exceptiongroup\\_formatting.py',
   'PYMODULE'),
  ('exceptiongroup._exceptions',
   'd:\\python\\lib\\site-packages\\exceptiongroup\\_exceptions.py',
   'PYMODULE'),
  ('exceptiongroup._version',
   'd:\\python\\lib\\site-packages\\exceptiongroup\\_version.py',
   'PYMODULE'),
  ('exceptiongroup._catch',
   'd:\\python\\lib\\site-packages\\exceptiongroup\\_catch.py',
   'PYMODULE'),
  ('loguru._string_parsers',
   'd:\\python\\lib\\site-packages\\loguru\\_string_parsers.py',
   'PYMODULE'),
  ('loguru._filters',
   'd:\\python\\lib\\site-packages\\loguru\\_filters.py',
   'PYMODULE'),
  ('loguru._colorama',
   'd:\\python\\lib\\site-packages\\loguru\\_colorama.py',
   'PYMODULE'),
  ('colorama',
   'd:\\python\\lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'd:\\python\\lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.ansi',
   'd:\\python\\lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.initialise',
   'd:\\python\\lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.winterm',
   'd:\\python\\lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorama.win32',
   'd:\\python\\lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('loguru._asyncio_loop',
   'd:\\python\\lib\\site-packages\\loguru\\_asyncio_loop.py',
   'PYMODULE'),
  ('loguru._defaults',
   'd:\\python\\lib\\site-packages\\loguru\\_defaults.py',
   'PYMODULE'),
  ('requests',
   'd:\\python\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.api',
   'd:\\python\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'd:\\python\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.packages',
   'd:\\python\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('flask', 'd:\\python\\lib\\site-packages\\flask\\__init__.py', 'PYMODULE'),
  ('flask.templating',
   'd:\\python\\lib\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'd:\\python\\lib\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.scaffold',
   'd:\\python\\lib\\site-packages\\flask\\scaffold.py',
   'PYMODULE'),
  ('flask.wrappers',
   'd:\\python\\lib\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'd:\\python\\lib\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug',
   'd:\\python\\lib\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.test',
   'd:\\python\\lib\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'd:\\python\\lib\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'd:\\python\\lib\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'd:\\python\\lib\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'd:\\python\\lib\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'd:\\python\\lib\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'd:\\python\\lib\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'd:\\python\\lib\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'd:\\python\\lib\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'd:\\python\\lib\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'd:\\python\\lib\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'd:\\python\\lib\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   'd:\\python\\lib\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('secrets', 'd:\\python\\lib\\secrets.py', 'PYMODULE'),
  ('werkzeug.urls',
   'd:\\python\\lib\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'd:\\python\\lib\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.http',
   'd:\\python\\lib\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'd:\\python\\lib\\site-packages\\werkzeug\\datastructures.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'd:\\python\\lib\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'd:\\python\\lib\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'd:\\python\\lib\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'd:\\python\\lib\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'd:\\python\\lib\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'd:\\python\\lib\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('uuid', 'd:\\python\\lib\\uuid.py', 'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'd:\\python\\lib\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'd:\\python\\lib\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'd:\\python\\lib\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'd:\\python\\lib\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'd:\\python\\lib\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'd:\\python\\lib\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'd:\\python\\lib\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'd:\\python\\lib\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'd:\\python\\lib\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'd:\\python\\lib\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('jinja2', 'd:\\python\\lib\\site-packages\\jinja2\\__init__.py', 'PYMODULE'),
  ('jinja2.ext', 'd:\\python\\lib\\site-packages\\jinja2\\ext.py', 'PYMODULE'),
  ('jinja2.parser',
   'd:\\python\\lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'd:\\python\\lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'd:\\python\\lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'd:\\python\\lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.tests',
   'd:\\python\\lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.filters',
   'd:\\python\\lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'd:\\python\\lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'd:\\python\\lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.utils',
   'd:\\python\\lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.constants',
   'd:\\python\\lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'd:\\python\\lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'd:\\python\\lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'd:\\python\\lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.environment',
   'd:\\python\\lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.debug',
   'd:\\python\\lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'd:\\python\\lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'd:\\python\\lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'd:\\python\\lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'd:\\python\\lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'd:\\python\\lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'd:\\python\\lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('flask.signals',
   'd:\\python\\lib\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('flask.helpers',
   'd:\\python\\lib\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.globals',
   'd:\\python\\lib\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.sessions',
   'd:\\python\\lib\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.json.tag',
   'd:\\python\\lib\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('itsdangerous',
   'd:\\python\\lib\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'd:\\python\\lib\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'd:\\python\\lib\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'd:\\python\\lib\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'd:\\python\\lib\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'd:\\python\\lib\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'd:\\python\\lib\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'd:\\python\\lib\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('werkzeug.local',
   'd:\\python\\lib\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('flask.ctx', 'd:\\python\\lib\\site-packages\\flask\\ctx.py', 'PYMODULE'),
  ('flask.config',
   'd:\\python\\lib\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.blueprints',
   'd:\\python\\lib\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('flask.app', 'd:\\python\\lib\\site-packages\\flask\\app.py', 'PYMODULE'),
  ('asgiref.sync',
   'd:\\python\\lib\\site-packages\\asgiref\\sync.py',
   'PYMODULE'),
  ('asgiref.local',
   'd:\\python\\lib\\site-packages\\asgiref\\local.py',
   'PYMODULE'),
  ('asgiref',
   'd:\\python\\lib\\site-packages\\asgiref\\__init__.py',
   'PYMODULE'),
  ('asgiref.current_thread_executor',
   'd:\\python\\lib\\site-packages\\asgiref\\current_thread_executor.py',
   'PYMODULE'),
  ('flask.testing',
   'd:\\python\\lib\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('click.testing',
   'd:\\python\\lib\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('click.core', 'd:\\python\\lib\\site-packages\\click\\core.py', 'PYMODULE'),
  ('click.decorators',
   'd:\\python\\lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.shell_completion',
   'd:\\python\\lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.parser',
   'd:\\python\\lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.globals',
   'd:\\python\\lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.exceptions',
   'd:\\python\\lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.types',
   'd:\\python\\lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click._compat',
   'd:\\python\\lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._winconsole',
   'd:\\python\\lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.utils',
   'd:\\python\\lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('click.termui',
   'd:\\python\\lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click._termui_impl',
   'd:\\python\\lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click.formatting',
   'd:\\python\\lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click._textwrap',
   'd:\\python\\lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('flask.logging',
   'd:\\python\\lib\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('flask.json.provider',
   'd:\\python\\lib\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('click', 'd:\\python\\lib\\site-packages\\click\\__init__.py', 'PYMODULE'),
  ('flask.cli', 'd:\\python\\lib\\site-packages\\flask\\cli.py', 'PYMODULE'),
  ('dotenv', 'd:\\python\\lib\\site-packages\\dotenv\\__init__.py', 'PYMODULE'),
  ('dotenv.ipython',
   'd:\\python\\lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'd:\\python\\lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.variables',
   'd:\\python\\lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('dotenv.parser',
   'd:\\python\\lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('flask.typing',
   'd:\\python\\lib\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('flask.json',
   'd:\\python\\lib\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE'),
  ('markupsafe',
   'd:\\python\\lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'd:\\python\\lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE')],
 [('python37.dll', 'd:\\python\\python37.dll', 'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'd:\\python\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('ucrtbase.dll', 'D:\\jdk22\\bin\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('Crypto\\Cipher\\_ARC4.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_ARC4.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_RIPEMD160.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_RIPEMD160.pyd',
   'BINARY'),
  ('cv2\\opencv_videoio_ffmpeg490_64.dll',
   'd:\\python\\lib\\site-packages\\cv2\\opencv_videoio_ffmpeg490_64.dll',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD4.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_MD4.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_poly1305.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_poly1305.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_chacha20.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_chacha20.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_x25519.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\PublicKey\\_x25519.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2b.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_BLAKE2b.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2s.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_BLAKE2s.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cfb.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_cfb.pyd',
   'BINARY'),
  ('Crypto\\Util\\_cpuid_c.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Util\\_cpuid_c.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA1.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_SHA1.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD5.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_MD5.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_pkcs1_decode.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_pkcs1_decode.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD2.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_MD2.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ec_ws.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\PublicKey\\_ec_ws.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_Salsa20.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_Salsa20.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA512.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_SHA512.pyd',
   'BINARY'),
  ('Crypto\\Protocol\\_scrypt.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Protocol\\_scrypt.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_blowfish.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_blowfish.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_portable.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_ghash_portable.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA384.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_SHA384.pyd',
   'BINARY'),
  ('libopenblas.XWYDX2IKJW2NMTWSFYNGFUWKQU3LYTCZ.gfortran-win_amd64.dll',
   'd:\\python\\lib\\site-packages\\numpy\\.libs\\libopenblas.XWYDX2IKJW2NMTWSFYNGFUWKQU3LYTCZ.gfortran-win_amd64.dll',
   'BINARY'),
  ('cv2\\opencv_videoio_ffmpeg480_64.dll',
   'd:\\python\\lib\\site-packages\\cv2\\opencv_videoio_ffmpeg480_64.dll',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aesni.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_aesni.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ctr.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_ctr.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des3.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_des3.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aes.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_aes.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cast.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_cast.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ecb.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_ecb.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed448.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\PublicKey\\_ed448.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ocb.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_ocb.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ofb.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_ofb.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA256.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_SHA256.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cbc.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_cbc.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_des.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed25519.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\PublicKey\\_ed25519.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_clmul.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_ghash_clmul.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_keccak.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_keccak.pyd',
   'BINARY'),
  ('Crypto\\Math\\_modexp.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Math\\_modexp.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA224.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Hash\\_SHA224.pyd',
   'BINARY'),
  ('Crypto\\Util\\_strxor.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Util\\_strxor.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_arc2.pyd',
   'd:\\python\\lib\\site-packages\\Crypto\\Cipher\\_raw_arc2.pyd',
   'BINARY'),
  ('select.pyd', 'd:\\python\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'd:\\python\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'd:\\python\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'd:\\python\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'd:\\python\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'd:\\python\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'd:\\python\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('pyexpat.pyd', 'd:\\python\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'd:\\python\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'd:\\python\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_decimal.pyd', 'd:\\python\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'd:\\python\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_cffi_backend.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\_cffi_backend.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'd:\\python\\lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\lapack_lite.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\linalg\\lapack_lite.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\random\\mtrand.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\random\\_sfc64.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\random\\_philox.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\random\\_pcg64.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\random\\_mt19937.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\random\\bit_generator.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\random\\_generator.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\random\\_common.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'd:\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\charset_normalizer\\md.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('cv2\\cv2.pyd', 'd:\\python\\lib\\site-packages\\cv2\\cv2.pyd', 'EXTENSION'),
  ('PIL\\_webp.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\PIL\\_webp.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\PIL\\_imagingtk.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\PIL\\_imagingcms.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\PIL\\_imaging.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('onnxruntime\\capi\\onnxruntime_pybind11_state.pyd',
   'd:\\python\\lib\\site-packages\\onnxruntime\\capi\\onnxruntime_pybind11_state.pyd',
   'EXTENSION'),
  ('_overlapped.pyd', 'd:\\python\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'd:\\python\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('markupsafe\\_speedups.cp37-win_amd64.pyd',
   'd:\\python\\lib\\site-packages\\markupsafe\\_speedups.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('libcrypto-1_1.dll', 'd:\\python\\DLLs\\libcrypto-1_1.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\jdk22\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libssl-1_1.dll', 'd:\\python\\DLLs\\libssl-1_1.dll', 'BINARY'),
  ('python3.dll', 'd:\\python\\python3.dll', 'BINARY'),
  ('MSVCP140.dll', 'C:\\WINDOWS\\system32\\MSVCP140.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\WINDOWS\\system32\\VCRUNTIME140_1.dll',
   'BINARY')],
 [],
 [],
 [('AES_PARAMS\\AES_MAIN.py',
   'E:\\智慧树爬题库\\智慧树查课\\AES_PARAMS\\AES_MAIN.py',
   'DATA'),
  ('AES_PARAMS\\__pycache__\\AES_MAIN.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\AES_PARAMS\\__pycache__\\AES_MAIN.cpython-37.pyc',
   'DATA'),
  ('MainSession\\Session.py',
   'E:\\智慧树爬题库\\智慧树查课\\MainSession\\Session.py',
   'DATA'),
  ('MainSession\\__pycache__\\Session.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\MainSession\\__pycache__\\Session.cpython-37.pyc',
   'DATA'),
  ('UserList\\UserKclist.py',
   'E:\\智慧树爬题库\\智慧树查课\\UserList\\UserKclist.py',
   'DATA'),
  ('UserList\\__pycache__\\UserKclist.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\UserList\\__pycache__\\UserKclist.cpython-37.pyc',
   'DATA'),
  ('UserLogin\\Login.py', 'E:\\智慧树爬题库\\智慧树查课\\UserLogin\\Login.py', 'DATA'),
  ('UserLogin\\__pycache__\\Login.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\UserLogin\\__pycache__\\Login.cpython-37.pyc',
   'DATA'),
  ('UserLogin\\__pycache__\\phone.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\UserLogin\\__pycache__\\phone.cpython-37.pyc',
   'DATA'),
  ('UserLogin\\__pycache__\\school.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\UserLogin\\__pycache__\\school.cpython-37.pyc',
   'DATA'),
  ('UserLogin\\phone.py', 'E:\\智慧树爬题库\\智慧树查课\\UserLogin\\phone.py', 'DATA'),
  ('UserLogin\\school.py', 'E:\\智慧树爬题库\\智慧树查课\\UserLogin\\school.py', 'DATA'),
  ('UserLogin\\转换cookie.py',
   'E:\\智慧树爬题库\\智慧树查课\\UserLogin\\转换cookie.py',
   'DATA'),
  ('YiDun\\__init__.py', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\__init__.py', 'DATA'),
  ('YiDun\\__pycache__\\__init__.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\YiDun\\__pycache__\\__init__.cpython-37.pyc',
   'DATA'),
  ('YiDun\\__pycache__\\captcha.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\YiDun\\__pycache__\\captcha.cpython-37.pyc',
   'DATA'),
  ('YiDun\\__pycache__\\cptcha_dian.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\YiDun\\__pycache__\\cptcha_dian.cpython-37.pyc',
   'DATA'),
  ('YiDun\\__pycache__\\gap.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\YiDun\\__pycache__\\gap.cpython-37.pyc',
   'DATA'),
  ('YiDun\\__pycache__\\path.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\YiDun\\__pycache__\\path.cpython-37.pyc',
   'DATA'),
  ('YiDun\\__pycache__\\打码.cpython-37.pyc',
   'E:\\智慧树爬题库\\智慧树查课\\YiDun\\__pycache__\\打码.cpython-37.pyc',
   'DATA'),
  ('YiDun\\captcha.py', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\captcha.py', 'DATA'),
  ('YiDun\\cptcha_dian.py', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\cptcha_dian.py', 'DATA'),
  ('YiDun\\gap.py', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\gap.py', 'DATA'),
  ('YiDun\\js\\actoken.js', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\js\\actoken.js', 'DATA'),
  ('YiDun\\js\\cb.js', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\js\\cb.js', 'DATA'),
  ('YiDun\\js\\fp.js', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\js\\fp.js', 'DATA'),
  ('YiDun\\js\\secureCaptcha.js',
   'E:\\智慧树爬题库\\智慧树查课\\YiDun\\js\\secureCaptcha.js',
   'DATA'),
  ('YiDun\\path.py', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\path.py', 'DATA'),
  ('YiDun\\打码.py', 'E:\\智慧树爬题库\\智慧树查课\\YiDun\\打码.py', 'DATA'),
  ('base_library.zip',
   'E:\\智慧树爬题库\\智慧树查课\\build\\chake\\base_library.zip',
   'DATA'),
  ('importlib_metadata-4.13.0.dist-info\\METADATA',
   'd:\\python\\lib\\site-packages\\importlib_metadata-4.13.0.dist-info\\METADATA',
   'DATA'),
  ('importlib_metadata-4.13.0.dist-info\\LICENSE',
   'd:\\python\\lib\\site-packages\\importlib_metadata-4.13.0.dist-info\\LICENSE',
   'DATA'),
  ('certifi\\cacert.pem',
   'd:\\python\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('importlib_metadata-4.13.0.dist-info\\top_level.txt',
   'd:\\python\\lib\\site-packages\\importlib_metadata-4.13.0.dist-info\\top_level.txt',
   'DATA'),
  ('cv2\\load_config_py3.py',
   'd:\\python\\lib\\site-packages\\cv2\\load_config_py3.py',
   'DATA'),
  ('cryptography-42.0.2.dist-info\\RECORD',
   'd:\\python\\lib\\site-packages\\cryptography-42.0.2.dist-info\\RECORD',
   'DATA'),
  ('cryptography-42.0.2.dist-info\\LICENSE.APACHE',
   'd:\\python\\lib\\site-packages\\cryptography-42.0.2.dist-info\\LICENSE.APACHE',
   'DATA'),
  ('certifi\\py.typed',
   'd:\\python\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('cryptography-42.0.2.dist-info\\INSTALLER',
   'd:\\python\\lib\\site-packages\\cryptography-42.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('importlib_metadata-4.13.0.dist-info\\RECORD',
   'd:\\python\\lib\\site-packages\\importlib_metadata-4.13.0.dist-info\\RECORD',
   'DATA'),
  ('cryptography-42.0.2.dist-info\\WHEEL',
   'd:\\python\\lib\\site-packages\\cryptography-42.0.2.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-42.0.2.dist-info\\top_level.txt',
   'd:\\python\\lib\\site-packages\\cryptography-42.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('cryptography-42.0.2.dist-info\\LICENSE.BSD',
   'd:\\python\\lib\\site-packages\\cryptography-42.0.2.dist-info\\LICENSE.BSD',
   'DATA'),
  ('cryptography-42.0.2.dist-info\\REQUESTED',
   'd:\\python\\lib\\site-packages\\cryptography-42.0.2.dist-info\\REQUESTED',
   'DATA'),
  ('importlib_metadata-4.13.0.dist-info\\INSTALLER',
   'd:\\python\\lib\\site-packages\\importlib_metadata-4.13.0.dist-info\\INSTALLER',
   'DATA'),
  ('cv2\\config.py', 'd:\\python\\lib\\site-packages\\cv2\\config.py', 'DATA'),
  ('cryptography-42.0.2.dist-info\\LICENSE',
   'd:\\python\\lib\\site-packages\\cryptography-42.0.2.dist-info\\LICENSE',
   'DATA'),
  ('importlib_metadata-4.13.0.dist-info\\WHEEL',
   'd:\\python\\lib\\site-packages\\importlib_metadata-4.13.0.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-42.0.2.dist-info\\METADATA',
   'd:\\python\\lib\\site-packages\\cryptography-42.0.2.dist-info\\METADATA',
   'DATA'),
  ('cv2\\config-3.py',
   'd:\\python\\lib\\site-packages\\cv2\\config-3.py',
   'DATA'),
  ('wheel-0.42.0.dist-info\\INSTALLER',
   'd:\\python\\lib\\site-packages\\wheel-0.42.0.dist-info\\INSTALLER',
   'DATA'),
  ('wheel-0.42.0.dist-info\\entry_points.txt',
   'd:\\python\\lib\\site-packages\\wheel-0.42.0.dist-info\\entry_points.txt',
   'DATA'),
  ('wheel-0.42.0.dist-info\\LICENSE.txt',
   'd:\\python\\lib\\site-packages\\wheel-0.42.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.42.0.dist-info\\RECORD',
   'd:\\python\\lib\\site-packages\\wheel-0.42.0.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.42.0.dist-info\\METADATA',
   'd:\\python\\lib\\site-packages\\wheel-0.42.0.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.42.0.dist-info\\WHEEL',
   'd:\\python\\lib\\site-packages\\wheel-0.42.0.dist-info\\WHEEL',
   'DATA'),
  ('cv2\\__init__.py',
   'd:\\python\\lib\\site-packages\\cv2\\__init__.py',
   'DATA'),
  ('cv2\\version.py',
   'd:\\python\\lib\\site-packages\\cv2\\version.py',
   'DATA'),
  ('cv2\\utils\\__init__.py',
   'd:\\python\\lib\\site-packages\\cv2\\utils\\__init__.py',
   'DATA'),
  ('cv2\\typing\\__init__.py',
   'd:\\python\\lib\\site-packages\\cv2\\typing\\__init__.py',
   'DATA'),
  ('cv2\\misc\\version.py',
   'd:\\python\\lib\\site-packages\\cv2\\misc\\version.py',
   'DATA'),
  ('cv2\\misc\\__init__.py',
   'd:\\python\\lib\\site-packages\\cv2\\misc\\__init__.py',
   'DATA'),
  ('cv2\\mat_wrapper\\__init__.py',
   'd:\\python\\lib\\site-packages\\cv2\\mat_wrapper\\__init__.py',
   'DATA'),
  ('cv2\\gapi\\__init__.py',
   'd:\\python\\lib\\site-packages\\cv2\\gapi\\__init__.py',
   'DATA'),
  ('cv2\\data\\__init__.py',
   'd:\\python\\lib\\site-packages\\cv2\\data\\__init__.py',
   'DATA')],
 [])
