from AES_PARAMS.AES_MAIN import *
import random
import string

def generate_random_imei():
    # IMEI号码通常由15位数字组成，但这里为了匹配示例中的格式，我们使用15位字符
    return ''.join(random.choices(string.ascii_letters + string.digits, k=15))


APP_LOGIN_API = f"https://appcomm-user-api.zhihuishu.com/app-commserv-user/gateway/f/newLogin/byAccountV5"

class start_phone:
    def __init__(self,session,username,password):
        self.session = session
        self.username = username
        self.password = password
    def login_status(self,token,validate):
        imei = generate_random_imei() # 08b2e2ce2824cc70
        data = {
            "deviceId": imei,
            "password": self.password,
            "areaCode": "86",
            "appType": "ZD_A",
            "appVersion": "4.9.8",
            "clientType": "1",
            "imei": imei,
            "token": token,
            "validate": validate,
            "version": "6",
            "account": self.username,
            "deviceNumber": imei,
            "zhsDeviceId": imei
        }
        secretStr = encrypt_login(data,'59d78f063c204a99')
        params = {
            "osVersion": "2",
            "secretStr" :secretStr,
            "timeNote": "**********"
        }
        r = self.session.post(APP_LOGIN_API,params=params,headers={
            "User-Agent": "android.zhihuishu.coma_zd",
            "z-version": "a_226",
            "Content-Type": "application/x-www-form-urlencoded",
            "Host": "appcomm-user-api.zhihuishu.com"
            }).json()
        return r